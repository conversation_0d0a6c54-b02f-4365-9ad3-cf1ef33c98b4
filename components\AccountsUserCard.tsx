import React, { useState } from "react";
import { View, Text, TouchableOpacity, TextInput, Pressable } from "react-native";
import { Ionicons } from "@expo/vector-icons";

import { showToastSuccess, showToastError } from "../hooks/toast";
import customFetchWithToken from "../app/utils/axiosInterceptor";
import ConfirmationModal from "./ConfirmationModal";

interface AccCard {
  id: number;
  item: Array<{ key: string; value: string }>;
  method: string;
  onUpdate?: () => void;
  currency: string;
  country: string;
}

const AccountCard = ({ id, item, method, onUpdate, currency, country }: AccCard) => {
 
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [editedValues, setEditedValues] =
    useState<Array<{ key: string; value: string }>>(item);

  const shortenWalletAddress = (
    address: string,
    startLength = 8,
    endLength = 6
  ) => {
    if (!address) return "";
    return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
  };

  const showDeleteConfirmation = () => {
    setIsDeleteModalVisible(true);
  };

  const handleDeleteUserPayments = async () => {
    try {
      const res = await customFetchWithToken.delete(
        `/delete-user-payment-fields-data/${id}`
      );
      showToastSuccess(res.data.message);
      if (onUpdate) onUpdate();
    } catch (error: any) {
      showToastError(error.response.data.message || "Error deleting payment.");
    }
  };

  const handleEditPayment = () => {
    setIsEditMode(true);
  };

  const handleSaveEdit = async () => {
    try {
      const res = await customFetchWithToken.put(
        `/edit-user-payment-fields-data/${id}`,
        {
          data: editedValues,
          payment_method: method,
        }
      );
      showToastSuccess("Payment details updated successfully");
      setIsEditMode(false);
      if (onUpdate) onUpdate();
    } catch (error: any) {
      showToastError(
        error.response.data.message || "Error updating payment details."
      );
    }
  };

  const handleCancelEdit = () => {
    setEditedValues(item);
    setIsEditMode(false);
  };

  const handleValueChange = (index: number, newValue: string) => {
    const newValues = [...editedValues];
    newValues[index] = { ...newValues[index], value: newValue };
    setEditedValues(newValues);
  };

  // Get payment method icon
  const getPaymentIcon = () => {
    const methodLower = method.toLowerCase();
    if (methodLower.includes('bank')) return "card-outline";
    if (methodLower.includes('paypal')) return "logo-paypal";
    if (methodLower.includes('crypto') || methodLower.includes('wallet')) return "wallet-outline";
    return "cash-outline";
  };

  const renderViewMode = () => (
    <View className="rounded-xl border border-gray-200 p-5 mb-4 bg-white shadow-sm">
      <View className="flex-row justify-between items-center mb-4">
        <View className="flex-row items-center space-x-2">
          <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center">
            <Ionicons name={getPaymentIcon()} size={22} color="#6366F1" />
          </View>
          <Text className="text-lg font-bold text-gray-800">{method}</Text>
        </View>
        
        <View className="flex-row space-x-3">
          <TouchableOpacity 
            className="p-2 rounded-full bg-gray-100"
            onPress={handleEditPayment}
          >
            <Ionicons name="pencil" size={20} color="#4F46E5" />
          </TouchableOpacity>
          <TouchableOpacity 
            className="p-2 rounded-full bg-red-50"
            onPress={showDeleteConfirmation}
          >
            <Ionicons name="trash-outline" size={20} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>

      <View className="border-t border-gray-100 pt-3 mt-1">
        {item.map((entry, index) => (
          <View key={index} className="mb-3">
            <Text className="text-sm font-medium text-gray-500">{entry.key}</Text>
            <Text className="text-base font-semibold text-gray-800">
              {typeof entry.value === "string" && entry.value.startsWith("0x")
                ? shortenWalletAddress(entry.value)
                : entry.value}
            </Text>
          </View>
        ))}

        <View className="flex-row flex-wrap mt-3 pt-3 border-t border-gray-100">
          <View className="w-1/2 mb-3">
            <Text className="text-sm font-medium text-gray-500">Currency</Text>
            <Text className="text-base font-semibold text-gray-800">{currency}</Text>
          </View>
          <View className="w-1/2 mb-3">
            <Text className="text-sm font-medium text-gray-500">Country</Text>
            <Text className="text-base font-semibold text-gray-800">{country}</Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderEditMode = () => (
    <View className="rounded-xl border border-indigo-200 p-5 mb-4 bg-white shadow-sm">
      <View className="flex-row justify-between items-center mb-5 pb-3 border-b border-indigo-100">
        <Text className="text-lg font-bold text-indigo-800">Edit {method} Details</Text>
        <View className="flex-row space-x-3">
          <TouchableOpacity 
            className="p-2 rounded-full bg-green-100"
            onPress={handleSaveEdit}
          >
            <Ionicons name="checkmark" size={20} color="#10B981" />
          </TouchableOpacity>
          <TouchableOpacity 
            className="p-2 rounded-full bg-gray-100"
            onPress={handleCancelEdit}
          >
            <Ionicons name="close" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      <View>
        {editedValues.map((entry, index) => (
          <View key={index} className="mb-4">
            <Text className="text-sm font-medium text-gray-600 mb-1">
              {entry.key}
            </Text>
            <TextInput
              maxLength={100}
              className="border border-gray-300 rounded-lg p-3 text-base bg-gray-50"
              value={entry.value}
              onChangeText={(text) => handleValueChange(index, text)}
              placeholder={`Enter ${entry.key.toLowerCase()}`}
              selectTextOnFocus
            />
          </View>
        ))}

        <View className="mt-3 pt-3 border-t border-gray-200">
          <Text className="text-sm font-medium text-gray-500">Payment method</Text>
          <Text className="text-base font-semibold text-gray-800">{method}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-white">
      {isEditMode ? renderEditMode() : renderViewMode()}
      
      <ConfirmationModal 
        isVisible={isDeleteModalVisible}
        onClose={() => setIsDeleteModalVisible(false)}
        onConfirm={handleDeleteUserPayments}
        title="Delete Payment Method"
        message={`Are you sure you want to delete this ${method} payment method?`}
      />
    </View>
  );
};

export default AccountCard;