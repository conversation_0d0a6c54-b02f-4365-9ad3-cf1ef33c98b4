import { useEffect, useState } from "react";
import {
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  Pressable,
  View,
} from "react-native";
import NotificationCard from "./NotificationCard";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import { toastConfig } from "@/hooks/toast";
import Toast from "react-native-toast-message";
import { BlurView } from "expo-blur";
import EventSource from "react-native-sse";
import { useWebsocketContext } from "@/app/context/AuthContext";
import * as SecureStore from "expo-secure-store";
import refreshTokenApi from "@/app/api/onboarding/refreshTokenEndpoint";
import { AxiosError } from "axios";
interface Props {
  isVisible: boolean;
  onClose: () => void;
  handleNotiCount: (data: number) => void;
}

interface Notification_json {
  listing_id: number;
  order_id: string;
  trade_amount: number;
}

interface notification {
  created_date: string;
  expire_date: string | null;
  id: number;
  notifiaction_msg: string;
  notification_json: Notification_json | null;
  read: boolean;
  type: string;
  user: number;
}

const Notifications = ({ isVisible, onClose, handleNotiCount }: Props) => {
  const { token } = useWebsocketContext();
  const [notificationsArr, setNotificationsArr] = useState<notification[]>([]);
  let sseErrorCount = 0;

  const clearAllNotifications = async () => {
    try {
      await customFetchWithToken.put("/clear-all-notification/");
      // getAllNotificationsHandler();
    } catch (error) {

      console.log(error);
    }
  };


  const refreshApiCall = async () => {
    try {
      const res = await refreshTokenApi();
      await SecureStore.setItemAsync("user", res.data.data.access_token);
      return res;
    } catch (error) {
      if (error) {
          console.log("shit happened !!", error); 
      }
      console.log(error);
      throw error;
    }
  };

  // const getAllNotificationsHandler = async () => {
  //   const url = new URL("https://dev.remflow.net/remflow/notification-sse/");

  //   try {
  //     const es = new EventSource(url, {
  //       withCredentials: true,
  //       headers: {
  //         Authorization: {
  //           toString: function () {
  //             return "Bearer " + token;
  //           },
  //           Accept: "text/event-stream",
  //           Connection: "keep-alive",
  //         },
  //       },
  //     });

  //     es.addEventListener("open", () => {
  //       console.log("Open SSE connection.");
  //     });

  //     es.addEventListener("message", (event) => {
  //       console.log("New message event:", event.data);
  //     });
  //     es.addEventListener("error", (event) => {
  //       if (sseErrorCount < 2) {
  //         if (event.type === "error") {
  //           console.error("Connection error:", event.message);
  //         } else if (event.type === "exception") {
  //           console.error("Error:", event.message, event.error);
  //         }
  //         sseErrorCount++;
  //       }
  //     });
  //     es.addEventListener("close", (event) => {
  //       console.log("Close SSE connection.");
  //     });
  //   } catch (error) {
  //     if (error instanceof AxiosError && error.response?.status === 401) {
  //       refreshApiCall();
  //     }
  //     console.log(error);
  //   }
  // };

  const getAllNotificationsHandler = async () => {
    try {
      const res = await customFetchWithToken("/get-notification/");
      handleNotiCount(res.data.count);
      const tempNotificationArr = res.data.data.reverse();
      setNotificationsArr(tempNotificationArr);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
     getAllNotificationsHandler();
  }, []);

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <BlurView intensity={100} tint="dark" className="flex-1">
        <View className="flex-1 justify-center items-center">
          {/* Background press to close */}
          <Pressable className="absolute w-full h-full" onPress={onClose} />

          {/* Content container - stops event propagation */}
          <View
            className="w-[95%] max-h-[80%] bg-white rounded-lg p-4"
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => e.stopPropagation()}
          >
            <TouchableOpacity
              onPress={clearAllNotifications}
              className="mb-4 bg-gray-100 p-3 rounded border border-gray-300 items-center"
            >
              <Text className="text-gray-800 font-medium">
                Clear Notifications
              </Text>
            </TouchableOpacity>

            <ScrollView
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={{
                paddingBottom: 20,
                flexGrow: 1, // Add this to ensure content can scroll
              }}
              className="space-y-2"
              scrollEventThrottle={16}
            >
              {notificationsArr.length > 0 ? (
                notificationsArr.map((el) => (
                  <NotificationCard
                    key={el.id}
                    id={el.id}
                    type={el.type}
                    created_date={el.created_date}
                    notifiaction_msg={el.notifiaction_msg}
                    orderid={el.notification_json?.order_id}
                  />
                ))
              ) : (
                <Text className="text-center mt-4 text-lg font-medium text-gray-700">
                  No Notifications
                </Text>
              )}
            </ScrollView>
          </View>
        </View>
      </BlurView>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default Notifications;
