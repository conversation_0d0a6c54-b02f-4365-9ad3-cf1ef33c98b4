import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { showToastSuccess, showToastError } from "../hooks/toast";
import customFetchWithToken from "../app/utils/axiosInterceptor";
import ConfirmationModal from "./ConfirmationModal";
import EditRecipientModal from "./EditRecipientModal";

interface PaymentDetail {
  key: string;
  value: string;
}

interface RecipientCardProps {
  userRecipientPaymentData: PaymentDetail[];
  PayMethodName: string | null;
  id: number;
  countryName: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  type: string;
  ubo_shareholder_date_of_incorporation: string | null;
  ubo_shareholder_name: string | null;
  dob: string | null;
  currency_payout: string | null;
  created_date: string;
  onDelete?: () => void;
  onEdit?: () => void;
}

const RecipientCard: React.FC<RecipientCardProps> = ({
  userRecipientPaymentData,
  PayMethodName,
  id,
  countryName,
  email,
  firstName,
  lastName,
  type,
  ubo_shareholder_date_of_incorporation,
  ubo_shareholder_name,
  dob,
  currency_payout,
  created_date,
  onDelete,
  onEdit,
}) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const shortenWalletAddress = (address: string, startLength = 8, endLength = 6) => {
    if (!address) return "";
    if (address.length <= startLength + endLength) return address;
    return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  const handleDeleteRecipient = async () => {
    try {
      const res = await customFetchWithToken.delete(`/delete-recipient/${id}`);
      showToastSuccess(res.data.message);
      if (onDelete) {
        onDelete();
      }
    } catch (error: any) {
      showToastError(error.response?.data?.message || "Error deleting recipient");
    }
  };

  const getRecipientName = () => {
    if (type === "Person") {
      return `${firstName || ""} ${lastName || ""}`.trim() || "Unnamed Person";
    }
    return ubo_shareholder_name || "Unnamed Business";
  };

  const getPaymentDisplayValue = (key: string, value: string) => {
    const isWalletAddress = key.toLowerCase().includes("wallet") || 
                           key.toLowerCase().includes("address") ||
                           PayMethodName?.toLowerCase().includes("crypto");
    
    if (isWalletAddress && value.length > 20) {
      return shortenWalletAddress(value);
    }
    return value;
  };

  return (
    <View className="bg-white rounded-2xl shadow-sm border border-gray-100 mb-4 overflow-hidden">
      {/* Header Section */}
      <View className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-100">
        <View className="flex-row justify-between items-start">
          <View className="flex-1 pr-3">
            <View className="mb-2">
              <View className="flex-row items-center mb-4">
                <View className={`w-3 h-3 rounded-full mr-2 ${type === "Person" ? "bg-green-400" : "bg-blue-400"}`} />
                <Text className="text-[16px] font-bold text-gray-800 flex-1" numberOfLines={2}>
                  {getRecipientName()}
                </Text>
              </View>
              <View className="flex-row mb-2">
                <View className={`px-2 py-1 rounded-full ${type === "Person" ? "bg-green-100" : "bg-blue-100"}`}>
                  <Text className={`text-xs font-medium ${type === "Person" ? "text-green-700" : "text-blue-700"}`}>
                    {type}
                  </Text>
                </View>
              </View>
            </View>
            
            <View className="flex-row items-center mb-1">
              <Ionicons name="location-outline" size={14} color="#6B7280" />
              <Text className="text-md text-gray-600 ml-1">{countryName}</Text>
              <Text className="text-gray-400 mx-2">•</Text>
              <Text className="text-md text-gray-600">{currency_payout}</Text>
            </View>
            
            <View className="flex-row items-center">
              <Ionicons name="mail-outline" size={14} color="#6B7280" />
              <Text className="text-md text-gray-600 ml-1" style={{ lineHeight: 14 }} numberOfLines={1}>
                {email}
              </Text>
            </View>
          </View>

          {/* Action Icons */}
          <View className="flex-row items-center space-x-3">
            <TouchableOpacity
              onPress={() => setShowEditModal(true)}
              className="bg-blue-50 p-2 rounded-full"
              activeOpacity={0.7}
            >
              <Ionicons name="pencil" size={20} color="#3B82F6" />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setShowDeleteModal(true)}
              className="bg-red-50 p-2 rounded-full"
              activeOpacity={0.7}
            >
              <Ionicons name="trash-outline" size={20} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Additional Information Section */}
      <View className="p-4 border-b border-gray-50">
        <Text className="text-sm font-medium text-gray-700 mb-3">Additional Information</Text>
        
        <View className="space-y-3">
          {type === "Person" ? (
            <>
              {dob && (
                <View className="flex-row justify-between items-center bg-gray-50 p-3 rounded-lg">
                  <Text className="text-sm text-gray-600 flex-1 mr-2">Date of Birth</Text>
                  <Text className="text-sm font-medium text-gray-800 flex-1 text-right">
                    {formatDate(dob)}
                  </Text>
                </View>
              )}
            </>
          ) : (
            <>
              {ubo_shareholder_name && (
                <View className="flex-row justify-between items-center bg-gray-50 p-3 rounded-lg">
                  <Text className="text-sm text-gray-600 flex-1 mr-2">UBO Shareholder</Text>
                  <Text className="text-sm font-medium text-gray-800 flex-1 text-right">
                    {ubo_shareholder_name}
                  </Text>
                </View>
              )}
              {ubo_shareholder_date_of_incorporation && (
                <View className="flex-row justify-between items-center bg-gray-50 p-3 rounded-lg">
                  <Text className="text-sm text-gray-600 flex-1 mr-2">Incorporation Date</Text>
                  <Text className="text-sm font-medium text-gray-800 flex-1 text-right">
                    {formatDate(ubo_shareholder_date_of_incorporation)}
                  </Text>
                </View>
              )}
            </>
          )}
          
          <View className="flex-row justify-between items-center bg-gray-50 p-3 rounded-lg">
            <Text className="text-sm text-gray-600 flex-1 mr-2">Recipient ID</Text>
            <Text className="text-sm font-medium text-gray-800 flex-1 text-right">#{id}</Text>
          </View>
        </View>
      </View>

      {/* Payment Method Section */}
      <View className="p-4 border-b border-gray-50">
        <Text className="text-sm text-gray-500 mb-1">Payment Method</Text>
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            <View className="bg-blue-100 p-1.5 rounded-lg mr-2">
              <Ionicons name="card-outline" size={16} color="#3B82F6" />
            </View>
            <Text className="text-base font-semibold text-gray-800">
              {PayMethodName || "N/A"}
            </Text>
          </View>
          
          <Text className="text-sm text-gray-600 ml-2">
            Created {formatDate(created_date)}
          </Text>
        </View>
      </View>

      {/* Payment Details Section */}
      <View className="p-4">
        <Text className="text-sm font-medium text-gray-700 mb-3">Payment Details</Text>
        <View className="space-y-3">
          {userRecipientPaymentData.map((detail, index) => (
            <View 
              key={index} 
              className="flex-row justify-between items-center bg-gray-50 p-3 rounded-lg"
            >
              <Text className="text-sm text-gray-600 flex-1 mr-2">
                {detail.key}
              </Text>
              <Text className="text-sm font-medium text-gray-800 flex-1 text-right">
                {getPaymentDisplayValue(detail.key, detail.value)}
              </Text>
            </View>
          ))}
        </View>
      </View>

      <ConfirmationModal
        isVisible={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteRecipient}
        title="Delete Recipient"
        message={`Are you sure you want to delete ${getRecipientName()}? This action cannot be undone.`}
      />

      <EditRecipientModal
        isVisible={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={() => {
          setShowEditModal(false);
          if (onEdit) {
            onEdit();
          }
        }}
        recipientData={{
          id,
          firstName,
          lastName,
          email,
          countryName,
          type,
          dob,
          ubo_shareholder_name,
          ubo_shareholder_date_of_incorporation,
          currency_payout,
          PayMethodName,
          userRecipientPaymentData,
        }}
      />
    </View>
  );
};

export default RecipientCard;
