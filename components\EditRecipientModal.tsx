import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { styled } from "nativewind";
import Toast from "react-native-toast-message";
import customFetchWithToken from "@/app/utils/axiosInterceptor";

interface Country {
  id: number;
  country_name: string;
}

interface Field {
  key: string;
}

interface PaymentDetail {
  key: string;
  value: string;
}

interface PaymentOption {
  id: number;
  payment_method: string;
}

interface EditRecipientModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  recipientData: {
    id: number;
    firstName: string | null;
    lastName: string | null;
    email: string;
    countryName: string;
    type: string;
    dob: string | null;
    ubo_shareholder_name: string | null;
    ubo_shareholder_date_of_incorporation: string | null;
    currency_payout: string | null;
    PayMethodName: string | null;
    userRecipientPaymentData: PaymentDetail[];
  };
}

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTextInput = styled(TextInput);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);
const StyledKeyboardAvoidingView = styled(KeyboardAvoidingView);

const EditRecipientModal: React.FC<EditRecipientModalProps> = ({
  isVisible,
  onClose,
  onSuccess,
  recipientData,
}) => {
  const [selectedOption, setSelectedOption] = useState<string>("");
  const [citizenship, setCitizenship] = useState<string>("");
  const [countryTo, setCountryTo] = useState<Country[]>([]);
  const [currencyPayout, setCurrencyPayout] = useState("");
  const [payMethodPayout, setPayMethodPayout] = useState("");
  const [dataPayout, setDataPayout] = useState<PaymentOption[]>([]);
  const [totalFields, setTotalFields] = useState<Field[]>([]);
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetail[]>([]);
  const [dob, setDob] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [shareHolderEmail, setShareHolderEmail] = useState("");
  const [dateOfIncorporation, setDateOfIncorporation] = useState("");
  const [shareHolderName, setShareHolderName] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [companyName, setCompanyName] = useState("");

  const BaseURL = process.env.EXPO_PUBLIC_Base_URL;

  // Initialize form with existing data
  useEffect(() => {
    if (recipientData && isVisible) {
      setSelectedOption(recipientData.type);
      setCitizenship(recipientData.countryName);
      setCurrencyPayout(recipientData.currency_payout || "");
      setPayMethodPayout(recipientData.PayMethodName || "");
      setEmail(recipientData.email);
      setPaymentDetails(recipientData.userRecipientPaymentData || []);

      if (recipientData.type === "Person") {
        setFirstName(recipientData.firstName || "");
        setLastName(recipientData.lastName || "");
        setDob(recipientData.dob || "");
      } else if (recipientData.type === "Business") {
        setShareHolderName(recipientData.ubo_shareholder_name || "");
        setDateOfIncorporation(recipientData.ubo_shareholder_date_of_incorporation || "");
        setShareHolderEmail(recipientData.email);
        setCompanyName(recipientData.ubo_shareholder_name || ""); // Using shareholder name as company name for now
      }
    }
  }, [recipientData, isVisible]);

  const fetchCountries = async () => {
    try {
      const response = await customFetchWithToken.get("/country-list/?flag=to");
      setCountryTo(response.data.data);
    } catch (error) {
      console.error("Error fetching countries:", error);
      Toast.show({ type: "error", text1: "Error fetching countries" });
    }
  };

  const fetchCurrencyPayout = async () => {
    if (!citizenship) return;
    try {
      const response = await customFetchWithToken.get(
        `/select-country-get-currency?country=${citizenship}`
      );
      setCurrencyPayout(response.data.data.currency__currency_code);
    } catch (error) {
      console.error("Error fetching currency:", error);
    }
  };

  const fetchPaymentMethods = async () => {
    if (!currencyPayout) return;
    try {
      const response = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyPayout}`
      );
      const data = await response.json();
      setDataPayout(data.data);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    }
  };

  const fetchPaymentFields = async () => {
    if (!payMethodPayout || !currencyPayout) return;
    try {
      const response = await customFetchWithToken.get(
        `/user-payment-fields/?payment_method=${payMethodPayout}&currency=${currencyPayout}`
      );
      setTotalFields(response.data.data);
    } catch (error) {
      console.error("Error fetching payment fields:", error);
    }
  };

  const handleUpdateRecipient = async () => {
    if (totalFields.length > 0 && totalFields.length !== paymentDetails.length) {
      return Toast.show({ type: "error", text1: "Please fill all fields" });
    }

    let data;

    if (selectedOption === "Person") {
      data = {
        type: selectedOption,
        country: citizenship,
        email: email,
        currency_payout: currencyPayout,
        payout_option: payMethodPayout,
        firstname: firstName,
        lastname: lastName,
        dob: dob,
        data: paymentDetails,
      };
    } else if (selectedOption === "Business") {
      data = {
        email: shareHolderEmail,
        type: selectedOption,
        payout_option: payMethodPayout,
        ubo_shareholder_name: shareHolderName,
        ubo_shareholder_date_of_incorporation: dateOfIncorporation,
        country: citizenship,
        currency_payout: currencyPayout,
        company_name: companyName,
        data: paymentDetails,
      };
    } else {
      return Toast.show({ type: "error", text1: "Please select a recipient type" });
    }

    try {
      const response = await customFetchWithToken.put(`/update-recipient/${recipientData.id}/`, data);
      if (response.status === 200 || response.status === 201) {
        Toast.show({ type: "success", text1: "Recipient Updated Successfully" });
        onSuccess();
        onClose();
      }
    } catch (error: any) {
      console.error("Error updating recipient:", error);
      if (error.response?.data?.message?.data) {
        error.response.data.message.data.forEach((errorMsg: string) => {
          Toast.show({ type: "error", text1: errorMsg });
        });
      } else {
        Toast.show({ 
          type: "error", 
          text1: error.response?.data?.message || "Failed to update recipient" 
        });
      }
    }
  };

  const formatDateForInput = (dateString: string | null) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
    } catch {
      return dateString;
    }
  };

  useEffect(() => {
    fetchCountries();
  }, []);

  useEffect(() => {
    if (citizenship) fetchCurrencyPayout();
  }, [citizenship]);

  useEffect(() => {
    if (currencyPayout) fetchPaymentMethods();
  }, [currencyPayout]);

  useEffect(() => {
    if (payMethodPayout) fetchPaymentFields();
  }, [payMethodPayout]);

  return (
    <Modal visible={isVisible} transparent animationType="slide">
      <StyledView className="flex-1 bg-black/50">
        <StyledKeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          className="flex-1 justify-end"
        >
          <StyledView className="bg-white rounded-t-3xl h-[90%]">
            {/* Header */}
            <StyledView className="flex-row justify-between items-center px-6 py-4 border-b border-gray-100">
              <StyledText className="text-2xl font-bold text-gray-800">
                Edit Recipient
              </StyledText>
              <TouchableOpacity onPress={onClose}>
                <StyledText className="text-gray-500 text-2xl">×</StyledText>
              </TouchableOpacity>
            </StyledView>

            <StyledScrollView className="flex-1 px-6">
              {/* Recipient Type */}
              <StyledView className="my-4">
                <StyledText className="text-sm font-medium text-gray-600 mb-2">
                  Type
                </StyledText>
                <StyledView className="bg-gray-50 rounded-xl overflow-hidden">
                  <Picker
                    selectedValue={selectedOption}
                    onValueChange={(value) => setSelectedOption(value)}
                    className="bg-transparent"
                  >
                    <Picker.Item label="Select Type" value="" />
                    <Picker.Item label="Person" value="Person" />
                    <Picker.Item label="Business" value="Business" />
                  </Picker>
                </StyledView>
              </StyledView>

              {selectedOption === "Business" && (
                <StyledView className="mb-4">
                  <StyledText className="text-sm font-medium text-gray-600 mb-2">
                    Company Name
                  </StyledText>
                  <StyledTextInput
                    placeholder="Enter Company Name"
                    value={companyName}
                    onChangeText={(value) => setCompanyName(value)}
                    maxLength={260}
                    className="bg-gray-50 rounded-xl p-4 text-gray-800"
                  />
                </StyledView>
              )}

              {(selectedOption === "Business" || selectedOption === "Person") && (
                <StyledView className="mb-4">
                  <StyledText className="text-sm font-medium text-gray-600 mb-2">
                    Country Name
                  </StyledText>
                  <StyledView className="bg-gray-50 rounded-xl overflow-hidden">
                    <Picker
                      selectedValue={citizenship}
                      onValueChange={(value) => setCitizenship(value)}
                    >
                      <Picker.Item label="Select Country" value="" />
                      {countryTo.map((country) => (
                        <Picker.Item
                          key={country.id}
                          label={country.country_name}
                          value={country.country_name}
                        />
                      ))}
                    </Picker>
                  </StyledView>
                </StyledView>
              )}

              {selectedOption === "Person" && (
                <>
                  <StyledView className="flex-row gap-4 mb-4">
                    <StyledView className="flex-1">
                      <StyledText className="text-sm font-medium text-gray-600 mb-2">
                        First Name
                      </StyledText>
                      <StyledTextInput
                        placeholder="Enter First Name"
                        value={firstName}
                        onChangeText={(value) => setFirstName(value)}
                        maxLength={260}
                        className="bg-gray-50 rounded-xl p-4 text-gray-800"
                      />
                    </StyledView>
                    <StyledView className="flex-1">
                      <StyledText className="text-sm font-medium text-gray-600 mb-2">
                        Last Name
                      </StyledText>
                      <StyledTextInput
                        placeholder="Enter Last Name"
                        value={lastName}
                        onChangeText={(value) => setLastName(value)}
                        maxLength={260}
                        className="bg-gray-50 rounded-xl p-4 text-gray-800"
                      />
                    </StyledView>
                  </StyledView>

                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      Date of Birth
                    </StyledText>
                    <StyledTextInput
                      placeholder="YYYY-MM-DD"
                      maxLength={10}
                      value={formatDateForInput(dob)}
                      onChangeText={(value) => setDob(value)}
                      className="bg-gray-50 rounded-xl p-4 text-gray-800"
                    />
                  </StyledView>

                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      Contact Email
                    </StyledText>
                    <StyledTextInput
                      placeholder="Enter Contact Email"
                      value={email}
                      onChangeText={(value) => setEmail(value)}
                      maxLength={40}
                      keyboardType="email-address"
                      className="bg-gray-50 rounded-xl p-4 text-gray-800"
                    />
                  </StyledView>
                </>
              )}

              {selectedOption === "Business" && (
                <>
                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      Contact Email
                    </StyledText>
                    <StyledTextInput
                      placeholder="Enter Contact Email"
                      value={shareHolderEmail}
                      onChangeText={(value) => setShareHolderEmail(value)}
                      maxLength={260}
                      keyboardType="email-address"
                      className="bg-gray-50 rounded-xl p-4 text-gray-800"
                    />
                  </StyledView>

                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      UBO Shareholder Name
                    </StyledText>
                    <StyledTextInput
                      placeholder="Enter Shareholder Name"
                      value={shareHolderName}
                      onChangeText={(value) => setShareHolderName(value)}
                      maxLength={260}
                      className="bg-gray-50 rounded-xl p-4 text-gray-800"
                    />
                  </StyledView>

                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      UBO Shareholder Date of Incorporation
                    </StyledText>
                    <StyledTextInput
                      placeholder="YYYY-MM-DD"
                      maxLength={10}
                      value={formatDateForInput(dateOfIncorporation)}
                      onChangeText={(value) => setDateOfIncorporation(value)}
                      className="bg-gray-50 rounded-xl p-4 text-gray-800"
                    />
                  </StyledView>
                </>
              )}

              {(selectedOption === "Business" || selectedOption === "Person") && (
                <>
                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      Currency of Payout
                    </StyledText>
                    <StyledTextInput
                      placeholder="Please select a country first"
                      value={currencyPayout || "Please select a country first"}
                      editable={false}
                      className="bg-gray-50 rounded-xl p-4 text-gray-800"
                    />
                  </StyledView>

                  <StyledView className="mb-4">
                    <StyledText className="text-sm font-medium text-gray-600 mb-2">
                      Select Payment Method (TO)
                    </StyledText>
                    <StyledView className="bg-gray-50 rounded-xl overflow-hidden">
                      <Picker
                        selectedValue={payMethodPayout}
                        onValueChange={(value) => setPayMethodPayout(value)}
                      >
                        <Picker.Item
                          label="Please select a currency first"
                          value=""
                        />
                        {dataPayout?.map((PayMethodName) => (
                          <Picker.Item
                            key={PayMethodName.id}
                            label={PayMethodName.payment_method}
                            value={PayMethodName.payment_method}
                          />
                        ))}
                      </Picker>
                    </StyledView>
                  </StyledView>
                </>
              )}

              {/* Payment Details Fields */}
              {totalFields.map((field) => (
                <StyledView key={field.key} className="mb-4">
                  <StyledText className="text-sm font-medium text-gray-600 mb-2">
                    {field.key}
                  </StyledText>
                  <StyledTextInput
                    placeholder={`Enter ${field.key}`}
                    value={
                      paymentDetails.find((item) => item.key === field.key)?.value || ""
                    }
                    onChangeText={(value) => {
                      const updatedFields = paymentDetails.filter(
                        (item) => item.key !== field.key
                      );
                      setPaymentDetails([
                        ...updatedFields,
                        { key: field.key, value },
                      ]);
                    }}
                    maxLength={260}
                    className="bg-gray-50 rounded-xl p-4 text-gray-800"
                  />
                </StyledView>
              ))}
            </StyledScrollView>

            {/* Footer Actions */}
            <StyledView className="px-6 py-4 border-t border-gray-100">
              <StyledTouchableOpacity
                onPress={handleUpdateRecipient}
                className="bg-blue-600 py-4 rounded-xl mb-2"
              >
                <StyledText className="text-white text-center font-bold">
                  Update Recipient
                </StyledText>
              </StyledTouchableOpacity>
              <StyledTouchableOpacity
                onPress={onClose}
                className="bg-gray-200 py-4 rounded-xl"
              >
                <StyledText className="text-gray-700 text-center font-bold">
                  Cancel
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        </StyledKeyboardAvoidingView>
      </StyledView>
      <Toast />
    </Modal>
  );
};

export default EditRecipientModal; 