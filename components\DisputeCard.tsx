import React from "react";
import { View, Text, Image } from "react-native";

interface DisputeCardProps {
  disputeTitle: string;
  disputeOrderNumber: string;
  disputeEvidence: string;
  disputeComment: string;
}

const DisputeCard = ({
  disputeTitle,
  disputeOrderNumber,
  disputeEvidence,
  disputeComment,
}: DisputeCardProps) => {
  return (
    <View className="w-full border-gray-600 border-[1px] my-3 rounded-md">
      <View className="py-2 px-3">
        <Text className="font-pmedium text-md text-blue-600">
          {disputeTitle}
        </Text>
      </View>
      <View className="py-2 px-3 flex flex-row">
        <Text className="font-pmedium text-md  text-blue-600">
          Dispute Order Number:{" "}
        </Text>
        <Text className="font-pmedium text-md ">{disputeOrderNumber}</Text>
      </View>
      <View
        className="py-2 px-3 flex justify-center items-start
      "
      >
        <Text className="font-pmedium text-md text-blue-500">
          Dispute Evidence:{" "}
        </Text>
        <View>
          <Image
            source={{ uri: disputeEvidence }}
            style={{ width: 100, height: 100 }}
          />
        </View>
      </View>
      <View className="py-2 px-3 flex flex-row">
        <Text className="font-pmedium text-md text-blue-500">Comment : </Text>
        <Text className="font-pmedium text-md">{disputeComment}</Text>
      </View>
    </View>
  );
};

export default DisputeCard;
