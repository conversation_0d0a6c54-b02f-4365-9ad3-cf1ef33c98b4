import React, { useState, useEffect, useRef } from "react";
import {
  Safe<PERSON>reaView,
  FlatList,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import Layout from "@/components/Layout";
import customFetchWithToken from "./utils/axiosInterceptor";
import TradeReviewModal from "@/components/TradeReviewModal";
import HistoryCard from "@/components/HistoryCard";
import { showToastSuccess, showToastError } from "@/hooks/toast";

interface User {
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  username: string;
}

interface Listing {
  available_liquidity: number;
  buyer_trade_fee: number | null;
  country_from: string | null;
  country_to: string | null;
  created_date: string;
  currency_accepted: number;
  currency_payout: number;
  details: string;
  final_trade_fee: number;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  max_liquidity: number;
  min_liquidity: number;
  payin_option: number;
  payout_option: number;
  rf_commission: number | null;
  terms_and_conditions: string;
  time_limit: number;
  trade_fee: number;
  unique_id: string;
  updated_date: string;
  user: number;
  user_payment_option: number;
}

interface TradeRequest {
  available_liquidity: number;
  chat_id: string;
  created_date: string;
  expiry_date: string;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  listing: Listing;
  max_liquidity: number;
  min_liquidity: number;
  order_number: string;
  order_status:
    | "pending"
    | "expired"
    | "rejected"
    | "ongoing"
    | "completed"
    | "notified";
  payin_currency: string;
  payin_option: string;
  payment_proof: string | null;
  payout_currency: string;
  payout_option: string;
  peer_id: User;
  recipient_id: number | null;
  time_duration: number;
  trade_amount: number;
  updated_date: string;
  user: User;
}

type TradeRequestArray = TradeRequest[];



export default function Component() {
  const [selectedFilter, setSelectedFilter] = useState("All");
  const [tradeReqArr, setTradeReqArr] = useState<TradeRequestArray>([]);
  const [tradeStatus, setTradeStatus] = useState("");
  const [tabHeader, setTabHeader] = useState("");
  const [searchByOrderNo, setSearchByOrderNo] = useState("");
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const ITEMS_PER_PAGE = 10;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedTradeId, setSelectedTradeId] = useState<number | null>(null);

  // Debounce timer for search
  const searchTimeoutRef = useRef<any>(null);

  const handleAllTradeReq = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setLoading(true);
      setTradeReqArr([]);
      setPage(1);
    }
    setIsFetching(true);
    setError(null);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?page=${reset ? 1 : page}&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
      setTabHeader("All Results");
    } catch (error) {
      console.error("Error loading all trades:", error);
      setError("Failed to load trade requests. Please try again.");
      showToastError("Failed to load trade requests. Please try again.");
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const handleSenderTradeReq = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setLoading(true);
      setTradeReqArr([]);
      setPage(1);
    }
    setIsFetching(true);
    setError(null);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?type=sender&page=${
          reset ? 1 : page
        }&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
      setTabHeader("Sender Results");
    } catch (error) {
      console.error("Error loading sender trades:", error);
      setError("Failed to load sender trades. Please try again.");
      showToastError("Failed to load sender trades. Please try again.");
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const handlePeerTradeReq = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setLoading(true);
      setTradeReqArr([]);
      setPage(1);
    }
    setIsFetching(true);
    setError(null);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?type=peer&page=${
          reset ? 1 : page
        }&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
      setTabHeader("Peer Results");
    } catch (error) {
      console.error("Error loading peer trades:", error);
      setError("Failed to load peer trades. Please try again.");
      showToastError("Failed to load peer trades. Please try again.");
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const handlePress = (reset = false) => {
    if (selectedFilter === "All") {
      handleAllTradeReq(reset);
    } else if (selectedFilter === "Peer") {
      handlePeerTradeReq(reset);
    } else {
      handleSenderTradeReq(reset);
    }
  };

  // Pull to refresh handler
  const onRefresh = () => {
    setRefreshing(true);
    setError(null);
    setSearchByOrderNo("");
    setTradeStatus("");
    handlePress(true);
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Initial load and filter changes
  useEffect(() => {
    handlePress(true);
  }, [selectedFilter]);

  const searchByOrderNoHandler = async (text: string) => {
    setSearchByOrderNo(text);
    setError(null);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // If text is empty, reload all trades
    if (!text.trim()) {
      handlePress(true);
      return;
    }

    // Debounce search
    searchTimeoutRef.current = setTimeout(async () => {
      setLoading(true);
      setHasMore(false);

      try {
        const res = await customFetchWithToken.get(
          `/get-trade-request/?order_number=${text}&status=&limit=${ITEMS_PER_PAGE}`
        );
        setTradeReqArr(res.data.results);
      } catch (error) {
        console.error("Error searching orders:", error);
        setError("Failed to search orders. Please try again.");
        showToastError("Failed to search orders. Please try again.");
      } finally {
        setLoading(false);
      }
    }, 1000);
  };

  const handleTradeReqByStatus = async (value: string) => {
    setLoading(true);
    setTradeStatus(value);
    setPage(1);
    setHasMore(true);
    setError(null);
    setSearchByOrderNo(""); // Clear search when filtering by status

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?status=${value}&page=1&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr(res.data.results);
      setHasMore(res.data.next !== null);
      setPage(2);
      setTabHeader(`${value || "All"} Status Results`);
    } catch (error) {
      console.error("Error filtering by status:", error);
      setError("Failed to filter by status. Please try again.");
      showToastError("Failed to filter by status. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenReviewModal = (tradeId: number) => {
    setSelectedTradeId(tradeId);
    setIsModalVisible(true);
  };

  const handleSubmitReview = async (rating: number, comment: string) => {
    try {
      console.log(
        "Submitting review:",
        { rating, comment },
        "for trade:",
        selectedTradeId
      );
      // The actual submission is now handled in the TradeReviewModal component
      showToastSuccess("Review submitted successfully!");
    } catch (error) {
      console.error("Error submitting review:", error);
      showToastError("Failed to submit review. Please try again.");
    } finally {
      setIsModalVisible(false);
      setSelectedTradeId(null);
    }
  };

  useEffect(() => {
    handlePress(true);
  }, [selectedFilter]);

  const renderHeader = () => (
    <View className="mb-4">
      {/* Page Header */}
      <View className="mb-6">
        <Text className="text-3xl font-pbold text-gray-900 mb-2">Trade History</Text>
        <Text className="text-sm text-gray-600 font-pmedium">
          View your complete transaction and trading history
        </Text>
      </View>

      {/* Filters Section */}
      <View className="bg-white rounded-2xl p-5 mb-4 shadow-sm border border-gray-200">
        {/* Search Controls */}
        <View className="mb-6">
          {/* Search Input */}
          <View className="mb-4">
            <Text className="text-sm font-semibold text-gray-700 mb-2">
              Search by Order Number
            </Text>
            <View className="bg-gray-50 border-2 border-gray-200 rounded-lg">
              <TextInput
                placeholder="Enter order number..."
                className="p-3 text-sm"
                maxLength={260}
                value={searchByOrderNo}
                onChangeText={searchByOrderNoHandler}
              />
            </View>
            <Text className="text-xs text-gray-500 mt-1">
              Search for specific orders using their unique number
            </Text>
          </View>

          {/* Status Filter */}
          <View className="mb-4">
            <Text className="text-sm font-semibold text-gray-700 mb-2">
              Filter by Status
            </Text>
            <View className="bg-gray-50 border-2 border-gray-200 rounded-lg">
              <Picker
                selectedValue={tradeStatus}
                onValueChange={handleTradeReqByStatus}
                style={{ height: 50 }}
              >
                <Picker.Item label="All Status" value="" />
                <Picker.Item label="Pending" value="pending" />
                <Picker.Item label="Completed" value="completed" />
                <Picker.Item label="Rejected" value="rejected" />
                <Picker.Item label="Expired" value="expired" />
                <Picker.Item label="Ongoing" value="ongoing" />
              </Picker>
            </View>
            <Text className="text-xs text-gray-500 mt-1">
              Filter trades by their current status
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View className="flex-row justify-center space-x-3">
          {[
            { key: "All", icon: "📊", label: "All Trades", color: "green" },
            { key: "Peer", icon: "🤝", label: "Peer Trades", color: "blue" },
            { key: "Sender", icon: "📤", label: "Sender Trades", color: "orange" },
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              onPress={() => {
                setSelectedFilter(filter.key);
                setPage(1);
                setHasMore(true);
                setSearchByOrderNo("");
                setTradeStatus("");
              }}
              disabled={loading}
              className={`flex-1 py-3 px-4 rounded-lg border-2 flex-row items-center justify-center ${
                selectedFilter === filter.key
                  ? filter.color === "green"
                    ? "bg-green-600 border-green-600"
                    : filter.color === "blue"
                    ? "bg-blue-600 border-blue-600"
                    : "bg-orange-600 border-orange-600"
                  : "bg-white border-gray-300"
              }`}
            >
              <Text className="text-base mr-2">{filter.icon}</Text>
              <Text
                className={`text-sm font-semibold ${
                  selectedFilter === filter.key ? "text-white" : "text-gray-700"
                }`}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Results Header */}
      {tabHeader && (
        <View className="flex-row justify-between items-center bg-white rounded-xl p-4 mb-4 border border-gray-200">
          <Text className="text-lg font-semibold text-gray-900">{tabHeader}</Text>
          <View className="bg-gray-100 px-3 py-1 rounded-full">
            <Text className="text-sm font-medium text-gray-600">
              {tradeReqArr.length} {tradeReqArr.length === 1 ? "result" : "results"}
            </Text>
          </View>
        </View>
      )}

      {/* Error Message */}
      {error && (
        <View className="flex-row items-center bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
          <Text className="text-lg mr-3">⚠️</Text>
          <Text className="text-sm font-medium text-red-600 flex-1">{error}</Text>
        </View>
      )}
    </View>
  );

  return (
    <Layout>
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-1 px-4 pt-4">
          <FlatList
            data={tradeReqArr}
            keyExtractor={(_, index) => String(index)}
            ListHeaderComponent={renderHeader()}
            renderItem={({ item }) => (
              <HistoryCard
                data={item}
                onOpenReviewModal={handleOpenReviewModal}
              />
            )}
            onEndReached={() => {
              if (!searchByOrderNo && !loading && hasMore) {
                handlePress(false);
              }
            }}
            onEndReachedThreshold={0.5}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={["#3b82f6"]}
                tintColor="#3b82f6"
              />
            }
            ListEmptyComponent={
              loading ? (
                <View className="py-20 items-center">
                  <ActivityIndicator size="large" color="#3b82f6" />
                  <Text className="text-center font-medium text-gray-600 mt-4">
                    Loading trades...
                  </Text>
                  <Text className="text-center text-sm text-gray-500 mt-1">
                    Please wait while we fetch your trade history
                  </Text>
                </View>
              ) : (
                <View className="py-20 items-center">
                  <Text className="text-6xl mb-4 opacity-60">📊</Text>
                  <Text className="text-xl font-semibold text-gray-700 mb-2">No trades found</Text>
                  <Text className="text-center text-sm text-gray-500 max-w-xs">
                    {searchByOrderNo || tradeStatus
                      ? "Try adjusting your search criteria or filters"
                      : "You haven't made any trades yet. Start trading to see your history here."}
                  </Text>
                  {(searchByOrderNo || tradeStatus) && (
                    <TouchableOpacity
                      onPress={() => {
                        setSearchByOrderNo("");
                        setTradeStatus("");
                        handlePress(true);
                      }}
                      className="bg-blue-600 py-2 px-4 rounded-lg mt-4"
                    >
                      <Text className="text-white font-medium">Clear Filters</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )
            }
            ListFooterComponent={
              isFetching && !loading ? (
                <View className="py-4 items-center">
                  <ActivityIndicator size="small" color="#3b82f6" />
                  <Text className="text-sm text-gray-500 mt-2">Loading more trades...</Text>
                </View>
              ) : null
            }
            showsVerticalScrollIndicator={false}
            initialNumToRender={ITEMS_PER_PAGE}
            maxToRenderPerBatch={5}
            windowSize={7}
            updateCellsBatchingPeriod={50}
            removeClippedSubviews={true}
          />
        </View>
        {selectedTradeId !== null && (
          <TradeReviewModal
            isVisible={isModalVisible}
            onClose={() => {
              setIsModalVisible(false);
              setSelectedTradeId(null);
            }}
            onSubmit={handleSubmitReview}
            tradeOrderId={String(selectedTradeId)}
          />
        )}
      </SafeAreaView>
    </Layout>
  );
}
