import Toast, { BaseToast } from "react-native-toast-message";

export const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      text1NumberOfLines={5} // Allow more lines for text1
      style={{ 
        borderLeftColor: "green", 
        width: "90%",
        minHeight: 60,
        // maxHeight: 200, // Let's rely on content and number of lines for now
        flexGrow: 1, // Allow container to grow
        marginHorizontal: 16,
        marginTop: 40,
      }}
      contentContainerStyle={{ 
        paddingHorizontal: 15,
        paddingVertical: 10,
        flexGrow: 1, // Allow content container to grow
      }}
      text1Style={{
        fontSize: 14,
        fontWeight: "bold",
        // marginLeft: 20, // Removed to allow text to use full width of content container
        zIndex: 1000,
        flexWrap: 'wrap',
        flexShrink: 1,
        // flex: 1, // Removed as flexGrow on parent is preferred
      }}
      text2Style={{
        fontSize: 14,
        flexWrap: 'wrap',
        flexShrink: 1,
        // flex: 1, // Removed
      }}
    />
  ),
  error: (props: any) => (
    <BaseToast
      {...props}
      text1NumberOfLines={5} // Allow more lines for text1
      style={{ 
        borderLeftColor: "red", 
        width: "90%",
        minHeight: 60,
        // maxHeight: 200, // Let's rely on content and number of lines for now
        flexGrow: 1, // Allow container to grow
        marginHorizontal: 16,
        marginTop: 40,
      }}
      contentContainerStyle={{ 
        paddingHorizontal: 15,
        paddingVertical: 10,
        flexGrow: 1, // Allow content container to grow
      }}
      text1Style={{
        fontSize: 14,
        fontWeight: "bold",
        // marginLeft: 20, // Removed
        zIndex: 1000,
        flexWrap: 'wrap',
        flexShrink: 1,
        // flex: 1, // Removed
      }}
      text2Style={{
        fontSize: 14,
        flexWrap: 'wrap',
        flexShrink: 1,
        // flex: 1, // Removed
      }}
    />
  ),
};

export const showToastSuccess = (message: string) => {
  Toast.show({
    type: "success",
    text1: message,
    position: "top",
    visibilityTime: 4000,
  });
};

export const showToastError = (message: string) => {
  Toast.show({
    type: "error",
    text1: message,
    position: "top",
    visibilityTime: 4000,
  });
};