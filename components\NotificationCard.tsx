import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";
import { useRouter } from "expo-router";

interface Props {
  id: number;
  type: string;
  created_date: string;
  notifiaction_msg: string;
  orderid: string | undefined;
}
interface DataValue {
  type: string;
  message: string;
  error: string;
  data: {
    action: string;
    button: string;
    flag: string;
    message: string;
    next_action: string;
    order_id: string;
    stepper: string;
    time_line_state: string;
  };
}
interface WebSocketContext {
  connection: any; // Replace `any` with the actual type if available
  messageHistory: any[]; // Replace `any` with the actual type if available
  sendMessage: (message: string) => void;
  recentMessage: string | null;
  lastJsonMessage: DataValue | null;
}

const Notifications = ({
  id,
  type,
  created_date,
  notifiaction_msg,
  orderid,
}: Props) => {
  const router = useRouter();
  const [selectedCard, setSelectedCard] = useState<number>(0);

  const handleAcceptTradeMsg = async (
    orderid: string | undefined,
    id: number
  ) => {
    const payload = {
      order_id: orderid,
    };

    const response = await customFetchWithToken.post("/trade/accept-request/", {
      payload,
    });

    if (response.status === 200) {
      showToastSuccess(response.data.message);
    }
    
    setTimeout(() => {
      handleNotificationRemove(id);
      router.push(`/trade/${orderid}`);
    }, 2000);
  };

  const handleRejectTradeMsg = async (orderid: string | undefined) => {
    const payload = {
      order_id: orderid,
    };

    const response = await customFetchWithToken.post("/trade/reject-request/", {
      payload,
    });

    if (response.status === 200) {
      showToastSuccess(response.data.message);
    }

    setTimeout(() => {
      handleNotificationRemove(id);
      router.push(`/trade/${orderid}`);
    }, 2000);
    handleNotificationRemove(id);
  };

  // useEffect(() => {
  //   if (dataValue?.error && orderid === dataValue?.data?.order_id) {
  //     showToastError(dataValue?.error);
  //   }
  // }, [dataValue]);

  // if (dataValue?.data.action && orderid === dataValue?.data?.order_id) {
  //   showToastSuccess(dataValue?.message);
  // }

  const handleNotificationRemove = async (id: number) => {
    console.log(id);
    setSelectedCard(id);
    try {
      const res = await customFetchWithToken.put(
        `/read-notification/?notification_id=${id}`
      );
      console.log(res);
    } catch (error: any) {
      console.error(error);
      showToastError(error.response.data.message);
    }
  };

  return (
    <View
      className={
        selectedCard
          ? "translate-x-96 transition ease-in-out  hidden"
          : "w-full  bg-slate-100 p-3  border-[0.5px] my-1 rounded-md "
      }
    >
      <View className="flex flex-row justify-between ">
        <View>
          <Text className="font-pmedium text-lg">ID: {id}</Text>
        </View>
        <TouchableOpacity onPress={() => handleNotificationRemove(id)}>
          <Text className="font-pmedium">
            <MaterialIcons name="cancel" size={24} color="black" />
          </Text>
        </TouchableOpacity>
      </View>
      <View>
        <Text className="font-pmedium">Type: {type}</Text>
      </View>
      {type === "trade_request" && !notifiaction_msg.includes("cancelled") ? (
        <View className="flex flex-row w-32 justify-between my-4">
          <TouchableOpacity onPress={() => handleAcceptTradeMsg(orderid, id)}>
            <Text className="font-pmedium bg-green-500 px-4 py-2 rounded-md mr-4 text-gray-100 cursor-pointer">
              Accept
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleRejectTradeMsg(orderid)}>
            <Text className="font-pmedium bg-red-500 px-4 py-2 rounded-md mr-4 text-gray-100 cursor-pointer">
              Reject
            </Text>
          </TouchableOpacity>
        </View>
      ) : (
        ""
      )}
      <View>
        <Text className="font-pmedium">Message: {notifiaction_msg}</Text>
      </View>
      <View>
        <Text className="font-pmedium">Create On: {created_date}</Text>
      </View>
    </View>
  );
};

export default Notifications;
