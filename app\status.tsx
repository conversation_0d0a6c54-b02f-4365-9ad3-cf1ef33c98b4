import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { Ionicons } from "@expo/vector-icons";
import { Link } from "expo-router";
import customFetchWithToken from "./utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";

export default function KYCForm() {
  const router = useRouter();
  const [annualIncomeUser, setAnnualIncomeUser] = useState("");
  const [govtTaxId, setGovtTaxId] = useState("");
  const [primaryBank, setPrimaryBank] = useState("");
  const [otherFinancialProviders, setOtherFinancialProviders] = useState("");
  const [otherproviders, setOtherProvider] = useState("");
  const [loading, setLoading] = useState(false);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [kycVerified, setKycVerified] = useState(false);
  const [kycVerificationFailed, setKycVerificationFailed] = useState(null);
  const [alreadyGenerated, setAlreadyGenerated] = useState(null);
  const [kycUrl, setKycUrl] = useState(null);
  const [userID, setUserID] = useState<string>("");

  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };
  const showToastSuccess = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const showToastError = (message: string) => {
    Toast.show({
      type: "error", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };

  async function getValueFor(key: any) {
    try {
      let result = await SecureStore.getItemAsync(key);
      if (result) {
        console.log(`Value retrieved: ${result}`);
        setUserID(result);
        return result;
      } else {
        console.error("No value found for the given key.");
        return null;
      }
    } catch (error) {
      console.error("Error retrieving value from SecureStore:", error);
      showToastError("Error retrieving stored data.");
      return null;
    }
  }

  const getCode = async () => {
    try {
      const res = await getValueFor("userID");
      if (res) {
        setUserID(res);
      }
    } catch (error) {
      console.error("Error fetching user ID:", error);
      showToastError("Failed to retrieve user ID.");
    }
  };

  const fetchDetailsApi = async () => {
    try {
      const res = await customFetchWithToken.get("/get-document/");
      console.log("egegeg1", res.data.data.cleardil_status);
      setAnnualIncomeUser(res.data.data.annual_income_level);
      setGovtTaxId(res.data.data.government_tax_id_number);
      setPrimaryBank(res.data.data.name_of_primary_bank);
      setOtherFinancialProviders(
        res.data.data.other_financial_service_providers
      );
      setKycVerificationFailed(res.data.data.cleardil_status);
    } catch (error) {
      console.log(error);
    }
  };

  const formData = new FormData();
  formData.append("user_id", userID);
  formData.append("flag", "financial");
  formData.append("annual_income_level", annualIncomeUser);
  formData.append("government_tax_id_number", govtTaxId);
  formData.append("name_of_primary_bank", primaryBank);
  formData.append("other_financial_service_providers", otherFinancialProviders);
  formData.append("other_providers", otherproviders);

  const handleUserStatusDocsSubmit = async () => {
    const requiredFields = {
      annualIncomeUser: "Annual income level*",
      govtTaxId: "Government Tax ID number",
      primaryBank: "Name of Primary Bank*",
      otherFinancialProviders: "Other Financial service Providers*",
    };

    const fieldValues: any = {
      annualIncomeUser,
      govtTaxId,
      primaryBank,
      otherFinancialProviders,
    };

    for (const [field, label] of Object.entries(requiredFields)) {
      if (!fieldValues[field]) {
        showToastError(`${label} is required.`);
        return false;
      }
    }

    setLoading(true);
    setButtonDisabled(true);

    console.log("FormData content:");

    try {
      const res = await customFetchWithToken.post(
        "/upload-document/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data", // Explicitly specifying is optional; Axios sets it automatically for FormData
          },
        }
      );
      console.log("Response:", res.data);

      if (res.status === 200) {
        console.log("Submission successful:", res.data);
        showToastSuccess(res.data.message);
        setTimeout(() => router.push("/searchads"), 1500);
      } else {
        console.error("Unexpected response:", res);
        showToastError("Submission failed. Please try again.");
      }
    } catch (error: any) {
      console.error("API Error:", error);

      if (error.response) {
        console.error("Error Response Data:", error.response.data);
        showToastError(
          error.response.data.message || "An unexpected error occurred."
        );
      } else if (error.request) {
        console.error("Error Request:", error.request);
        showToastError("No response received. Check your network or server.");
      } else {
        console.error("Error Message:", error.message);
        showToastError("Network Error. Please try again.");
      }
    } finally {
      setLoading(false);
      setButtonDisabled(false);
    }
  };

  useEffect(() => {
    getCode();
    fetchDetailsApi();
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="flex-1">
        <View className="p-4">
          <View className="flex-row justify-between items-center mb-6">
            <TouchableOpacity className="flex-row items-center">
              <Ionicons name="chevron-back" size={24} color="black" />
              <Text className="text-black text-lg ml-1">Back</Text>
            </TouchableOpacity>
            <View className="flex-row">
              <View className="w-8 h-8 rounded-full bg-blue-200 mr-2" />
              <View className="w-8 h-8 rounded-full bg-pink-200 mr-2" />
              <View className="w-12 h-12 rounded-full bg-blue-300 -mt-4 border-4 border-white">
                <View className="w-full h-full items-center justify-center">
                  <Ionicons name="person" size={24} color="white" />
                </View>
              </View>
            </View>
          </View>

          <View className="bg-white rounded-lg shadow-md p-4 mb-4">
            <Text className="text-gray-600 mb-2">
              Annual income level (in USD $)*
            </Text>
            <View className="border border-gray-300 rounded-md">
              <Picker
                selectedValue={annualIncomeUser}
                onValueChange={(itemValue) => setAnnualIncomeUser(itemValue)}
                className="h-12"
              >
                <Picker.Item label="Select Annual Income" value="" />
                <Picker.Item label="0 - 20k" value="0 - 20k" />
                <Picker.Item label="20k - 40k" value="20k - 40k" />
                <Picker.Item label="40k - 60k" value="40k - 60k" />
              </Picker>
            </View>

            <Text className="text-gray-600 mt-4 mb-2">
              Government Tax ID number
            </Text>
            <TextInput
              className="border border-gray-300 rounded-md p-2 mb-4"
              value={govtTaxId}
              onChangeText={setGovtTaxId}
              placeholder="Enter Govt Tax ID"
            />

            <Text className="text-gray-600 mb-2">Name of Primary Bank*</Text>
            <TextInput
              className="border border-gray-300 rounded-md p-2 mb-4"
              value={primaryBank}
              onChangeText={setPrimaryBank}
              placeholder="Enter Name of Primary Account"
            />

            <Text className="text-gray-600 mb-2">
              Financial service Providers*
            </Text>
            <View className="border border-gray-300 rounded-md">
              <Picker
                selectedValue={otherFinancialProviders}
                onValueChange={(itemValue) =>
                  setOtherFinancialProviders(itemValue)
                }
                className="h-12"
              >
                <Picker.Item label="select other providers" value="#" />
                <Picker.Item label="Wise" value="wise" />
                <Picker.Item label="Neteller" value="neteller" />
                <Picker.Item label="Skrill" value="skrill" />
                <Picker.Item label="Western Union" value="Western Union" />
                <Picker.Item label="PayPal" value="PayPal" />
                <Picker.Item label="Xoom" value="Xoom" />
                <Picker.Item label="Remitly" value="Remitly" />
                <Picker.Item label="Binance" value="Binance" />
                <Picker.Item label="Huobi" value="Huobi" />
                <Picker.Item label="Paxful" value="Paxful" />
                <Picker.Item label="Airtm" value="Airtm" />
                <Picker.Item label="Other" value="Other" />
              </Picker>
            </View>

            {otherFinancialProviders === "Other" && (
              <View className="mt-4">
                <Text className="text-gray-600 mb-2">
                  Input Other Financial Provider Name*
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-md p-2"
                  value={otherproviders}
                  onChangeText={setOtherProvider}
                  placeholder="Enter Other Financial Provider Name"
                />
              </View>
            )}
          </View>

          <TouchableOpacity
            className="bg-black py-3 rounded-md items-center"
            onPress={handleUserStatusDocsSubmit}
          >
            {/* <Link href="/profile"> */}
            <Text className="text-white font-bold text-lg">
              Complete your KYC here
            </Text>
            {/* </Link> */}
          </TouchableOpacity>
        </View>
      </ScrollView>
      <Toast config={toastConfig} />
    </SafeAreaView>
  );
}
