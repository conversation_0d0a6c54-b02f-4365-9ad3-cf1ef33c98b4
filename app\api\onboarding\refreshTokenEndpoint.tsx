import axios from "axios";
import * as SecureStore from "expo-secure-store";

const Base_url = process.env.EXPO_PUBLIC_Base_URL;

const refreshTokenApi = async () => {
  try {
    // Retrieve the refresh token securely from SecureStore
    const refreshToken = await SecureStore.getItemAsync("refreshToken");

    if (!refreshToken) {
      throw new Error("No refresh token available");
    }

    // Make the API request to refresh the access token
    const response = await axios({
      url: `${Base_url}/get-access-token/?refresh_token=${refreshToken}`,
      method: "GET",
    });

    return response;
  } catch (error) {
    console.error("Error fetching new access token:", error);
    throw error;
  }
};

export default refreshTokenApi;
