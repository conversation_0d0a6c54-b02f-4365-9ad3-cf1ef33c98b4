{"name": "rem_expo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["./jest.setup.ts"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@sentry/react-native|native-base|react-native-svg)"], "collectCoverage": true, "collectCoverageFrom": ["**/*.{ts,tsx,js,jsx}", "!**/coverage/**", "!**/node_modules/**", "!**/babel.config.js", "!**/expo-env.d.ts", "!**/.expo/**"]}, "dependencies": {"@babel/preset-env": "^7.26.0", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/native": "^7.0.0", "axios": "^1.7.9", "babel-preset-expo": "^12.0.4", "expo": "~52.0.46", "expo-blur": "~14.0.3", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.3", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.1", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "lodash": "^4.17.21", "nativewind": "^2.0.11", "postcss": "^8.4.49", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-ratings": "^8.1.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sse": "^1.2.1", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.13", "react-test-renderer": "18.3.1", "react-use-websocket": "^4.11.1", "stream-chat-expo": "^6.2.0", "stream-chat-react-native": "^6.2.0", "expo-linear-gradient": "~14.0.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@react-native-community/cli": "latest", "@testing-library/react-native": "^13.2.0", "@types/base-64": "^1.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.2.0", "jest": "^29.7.0", "jest-expo": "~52.0.6", "react-test-renderer": "18.2.0", "tailwindcss": "^3.3.2", "typescript": "~5.7.2"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@babel/preset-env", "lodash", "postcss", "react-use-websocket", "stream-chat"]}}}, "private": true}