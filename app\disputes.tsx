// Dispute Page in React Native Expo using  (NativeWind)
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Button,
  TextInput,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  ToastAndroid,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import axios from "axios";
import DisputeForm from "@/components/DisputeForm";
import { showToastError, toastConfig, showToastSuccess } from "@/hooks/toast";
import Toast, { BaseToast } from "react-native-toast-message";
import customFetchWithToken from "./utils/axiosInterceptor";
import Layout from "@/components/Layout";
import DisputeCard from "@/components/DisputeCard";

interface Dispute {
  id: number;
  admin_comment: string | null;
  chat_id: string | null;
  comments: string;
  created_date: string;
  disput_title__disput_title: string;
  is_deleted: boolean;
  order_id__order_number: string;
  query_title: string | null;
  status: string | null;
  upload_evidence: string;
}

const DisputePage = () => {
  const [showDisputes, setShowDisputes] = useState(true);
  const [showAddDisputes, setShowAddDisputes] = useState(false);
  const [selectedOption, setSelectedOption] = useState(-1);
  const [loadedDisputesList, setLoadedDisputesList] = useState([]);
  const [disputeTitle, setDisputeTitle] = useState("");

  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();
  const [loadedDisputesArr, setLoadedDisputesArr] = useState<Dispute[]>([]);
  const [loadedDisputesMessage, setLoadedDisputesMessage] = useState("");

  console.log("loadedDisputesArr", loadedDisputesArr);

  const showDisputeHandler = () => {
    setShowDisputes(true);
    setShowAddDisputes(false);
    fetchDisputes();
  };
  const showAddDisputeHandler = () => {
    setShowAddDisputes(true);
    setShowDisputes(false);
  };

  const fetchDisputes = async () => {
    setLoading(true);
    try {
      const response = await customFetchWithToken.get("/dispute-list");
      setLoadedDisputesArr(response.data.data);
      console.log("disputes", response);
    } catch (error: any) {
      showToastError("Failed to fetch disputes");
      console.log(error.response);
    } finally {
      setLoading(false);
    }
  };

  const fetchDisputeReasons = async () => {
    try {
      const response = await customFetchWithToken.get("/get-dispute-list");
      setLoadedDisputesList(response.data.data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchDisputes();
  }, []);
  //   useEffect(() => {
  //     fetchDisputeReasons();
  //     fetchDisputes();
  //   }, []);
  const reasons = [
    {
      title: "Funds not received on time",
    },
    {
      title: "Third party payment",
    },
    {
      title: "Adjust Amount (underpaid)",
    },
    {
      title: "Suspicious behaviour",
    },
    {
      title: "Others",
    },
  ];

  return (
    <Layout>
      <ScrollView className={"flex-1 p-4 bg-white"}>
        <View className={"flex-row justify-between mb-4"}>
          <TouchableOpacity onPress={showDisputeHandler}>
            <View className="bg-blue-500 px-10 py-3 rounded-md">
              <Text>Disputes</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={showAddDisputeHandler}>
            <View className="bg-blue-500 px-4 py-3 rounded-md">
              <Text>Add New Disputes</Text>
            </View>
          </TouchableOpacity>
        </View>
        {showDisputes && loadedDisputesArr.length === 0 && (
          <View>
            <Text className="text-center text-lg font-pmedium mt-6">
              No dispute found
            </Text>
          </View>
        )}
        {showDisputes && loadedDisputesArr.length > 0 && (
          <View>
            {loadedDisputesArr.map((el: any) => (
              <DisputeCard
                key={el.id}
                disputeTitle={el.disput_title__disput_title}
                disputeOrderNumber={el.order_id__order_number}
                disputeEvidence={el.upload_evidence}
                disputeComment={el.comments}
              />
            ))}
          </View>
        )}

        {!showDisputes && (
          <View className="w-full  border-cyan-500 border-[1px]  rounded-md">
            <View className="py-3">
              <Text className="text-center font-pmedium text-md">Reason</Text>
            </View>
            <View className="w-full flex justify-center items-center">
              {reasons.map((el, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => setDisputeTitle(el.title)}
                >
                  <View
                    key={index}
                    className="bg-blue-700 rounded-md my-4 w-72"
                  >
                    <Text className="px-8 py-5 font-pmedium text-yellow-50 text-center">
                      {el.title}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
            {disputeTitle && (
              <View>
                <DisputeForm title={disputeTitle} selectedId={1} />
              </View>
            )}
          </View>
        )}
        <Toast config={toastConfig} />
      </ScrollView>
    </Layout>
  );
};

export default DisputePage;
