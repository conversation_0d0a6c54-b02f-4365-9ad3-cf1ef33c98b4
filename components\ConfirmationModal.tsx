import React from 'react';
import {<PERSON><PERSON>, Modal, Text, Pressable, View} from 'react-native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import "nativewind"; // Ensure that NativeWind is imported
import { Ionicons } from '@expo/vector-icons';

interface ConfirmationModalProps {
  isVisible: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title?: string;
  message?: string;
}

const ConfirmationModal = ({
  isVisible, 
  onClose,
  onConfirm,
  title = "Confirmation",
  message = "Are you sure you want to continue?"
}: ConfirmationModalProps) => {
  return (
    <SafeAreaProvider>
      <SafeAreaView className="flex-1 justify-center items-center">
        <Modal
          animationType="fade"
          transparent={true}
          visible={isVisible}
          onRequestClose={() => {
            Alert.alert('Modal has been closed.');
            onClose();
          }}>
          <View className="flex-1 justify-center items-center bg-black/60">
            <View className="w-4/5 bg-white rounded-3xl p-6 shadow-xl">
              <View className="items-end mb-2">
                <Pressable 
                  className="p-1" 
                  onPress={onClose}
                  hitSlop={12}
                >
                  <Ionicons name="close" size={24} color="#6B7280" />
                </Pressable>
              </View>
              
              <Text className="text-center text-xl font-pbold mb-2 text-gray-800">
                {title}
              </Text>
              
              <Text className="text-center text-blue-600 font-pmedium mb-6 px-4">
                {message}
              </Text>
              
              <View className="flex-row justify-center space-x-4">
                <Pressable
                  className="bg-gray-200 py-3 px-6 rounded-xl active:bg-gray-300 w-32"
                  onPress={onClose}>
                  <Text className="text-gray-800 font-pbold text-center">Cancel</Text>
                </Pressable>
                
                <Pressable
                  className="bg-red-600 py-3 px-6 rounded-xl active:bg-blue-700 w-32"
                  onPress={() => {
                    if (onConfirm) onConfirm();
                    onClose();
                  }}>
                  <Text className="text-white font-pbold text-center">Confirm</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default ConfirmationModal;