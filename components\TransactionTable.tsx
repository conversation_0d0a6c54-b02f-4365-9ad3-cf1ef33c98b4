import React from "react";
import { View, Text, ScrollView } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Layout from "../components/Layout";

const TableHeader = () => {
  return (
    <View className="flex flex-row justify-between items-center bg-gray-200 p-4 rounded-t-md">
      <Text className="w-1/6 text-gray-800 font-pmedium text-xs">Date</Text>
      <Text className="w-1/6 text-gray-800 font-pmedium text-xs">Order Id</Text>

      <Text className="w-1/6 text-gray-800 font-pmedium text-xs">Amount</Text>
      <Text className="w-1/6 text-gray-800 font-pmedium text-xs">Currency</Text>
      <Text className="w-1/6 text-gray-800 font-pmedium text-xs">Status</Text>
    </View>
  );
};

const TransactionTable = ({
  date,
  traderId,
  orderId,
  amount,
  currency,
  status,
}) => {
  return (
    <View className="flex flex-row justify-between items-center bg-white p-4  rounded-md">
      <View className="w-1/6">
        <Text className="text-gray-800 font-pmedium text-xs">{date}</Text>
      </View>
      {/* <View className="w-1/6">
        <Text className="text-gray-800 font-pmedium text-xs">{traderId}</Text>
      </View> */}
      <View className="w-1/6">
        <Text className="text-gray-800 font-pmedium text-xs">{orderId}</Text>
      </View>
      <View className="w-1/6">
        <Text className="text-gray-800 font-pmedium text-xs">{amount}</Text>
      </View>
      <View className="w-1/6">
        <Text className="text-gray-800 font-pmedium text-xs">{currency}</Text>
      </View>
      <View className="w-1/6">
        <Text
          className={`font-pmedium text-xs ${
            status === "Done" ? "text-green-500" : "text-red-500"
          }`}
        >
          {status}
        </Text>
      </View>
    </View>
  );
};

const Transactions = () => {
  const transactionData = [
    {
      created_date: "10 June, 2023",
      traderId: "7854842",
      transaction_id: "457841",
      transaction_amount: "325.55",
      currency: "GBP",
      transaction_status: "Done",
    },
    {
      created_date: "12 June, 2023",
      traderId: "7854843",
      transaction_id: "457842",
      transaction_amount: "120.00",
      currency: "USD",
      transaction_status: "Pending",
    },
    {
      created_date: "13 June, 2023",
      traderId: "7854444",
      transaction_id: "457843",
      transaction_amount: "170.00",
      currency: "INR",
      transaction_status: "Done",
    },
    {
      created_date: "10 May, 2024",
      traderId: "7854445",
      transaction_id: "457844",
      transaction_amount: "220.00",
      currency: "USD",
      transaction_status: "Done",
    },
    {
      created_date: "10 May, 2024",
      traderId: "7854463",
      transaction_id: "457845",
      transaction_amount: "290.00",
      currency: "USD",
      transaction_status: "Done",
    },
    {
      created_date: "10 May, 2024",
      traderId: "7854465",
      transaction_id: "457846",
      transaction_amount: "820.00",
      currency: "EUR",
      transaction_status: "Pending",
    },
  ];

  return (
    <View>
      <Text className="text-left  mt-3 text-lg font-psemitBold">
        Transactions
      </Text>
      <View className="">
        <TableHeader />
        {transactionData.length > 0 ? (
          transactionData.map((el, index) => (
            <TransactionTable
              key={index}
              date={el.created_date}
              traderId={el.traderId}
              orderId={el.transaction_id}
              amount={el.transaction_amount}
              currency={el.currency}
              status={el.transaction_status}
            />
          ))
        ) : (
          <Text className="text-center text-gray-600 mt-4">
            No transactions available
          </Text>
        )}
      </View>
    </View>
  );
};

export default Transactions;
