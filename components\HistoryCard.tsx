import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import customFetchWithToken from "../app/utils/axiosInterceptor";
import { useRouter } from "expo-router";
import { showToastSuccess, showToastError } from "@/hooks/toast";

interface User {
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  username: string;
}

interface Listing {
  available_liquidity: number;
  buyer_trade_fee: number | null;
  country_from: string | null;
  country_to: string | null;
  created_date: string;
  currency_accepted: number;
  currency_payout: number;
  details: string;
  final_trade_fee: number;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  max_liquidity: number;
  min_liquidity: number;
  payin_option: number;
  payout_option: number;
  rf_commission: number | null;
  terms_and_conditions: string;
  time_limit: number;
  trade_fee: number;
  unique_id: string;
  updated_date: string;
  user: number;
  user_payment_option: number;
}

interface TradeRequest {
  available_liquidity: number;
  chat_id: string;
  created_date: string;
  expiry_date: string;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  listing: Listing;
  max_liquidity: number;
  min_liquidity: number;
  order_number: string;
  order_status:
    | "pending"
    | "expired"
    | "rejected"
    | "ongoing"
    | "completed"
    | "notified";
  payin_currency: string;
  payin_option: string;
  payment_proof: string | null;
  payout_currency: string;
  payout_option: string;
  peer_id: User;
  recipient_id: number | null;
  time_duration: number;
  trade_amount: number;
  updated_date: string;
  user: User;
}

// Status configuration interface
interface StatusConfig {
  icon: string;
  label: string;
  description: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

// Enhanced status configuration matching the web version
const getStatusConfig = (status: string): StatusConfig => {
  const statusMap: Record<string, StatusConfig> = {
    pending: {
      icon: "⏳",
      label: "Pending Approval",
      description: "Waiting for peer response",
      color: "#92400e",
      bgColor: "#fef3c7",
      borderColor: "#f59e0b",
    },
    expired: {
      icon: "⏰",
      label: "Expired",
      description: "Trade request has expired",
      color: "#374151",
      bgColor: "#f3f4f6",
      borderColor: "#9ca3af",
    },
    ongoing: {
      icon: "🔄",
      label: "In Progress",
      description: "Trade is currently active",
      color: "#1e40af",
      bgColor: "#dbeafe",
      borderColor: "#3b82f6",
    },
    rejected: {
      icon: "❌",
      label: "Rejected",
      description: "Trade request was declined",
      color: "#b91c1c",
      bgColor: "#fee2e2",
      borderColor: "#ef4444",
    },
    completed: {
      icon: "✅",
      label: "Completed",
      description: "Trade successfully finished",
      color: "#065f46",
      bgColor: "#d1fae5",
      borderColor: "#10b981",
    },
    notified: {
      icon: "🔔",
      label: "Notified",
      description: "Notification sent",
      color: "#3730a3",
      bgColor: "#e0e7ff",
      borderColor: "#6366f1",
    },
    cancelled: {
      icon: "🚫",
      label: "Cancelled",
      description: "Trade was cancelled",
      color: "#374151",
      bgColor: "#f3f4f6",
      borderColor: "#9ca3af",
    },
  };

  return statusMap[status.toLowerCase()] || {
    icon: "❓",
    label: status || "Unknown",
    description: "Status unknown",
    color: "#6b7280",
    bgColor: "#f9fafb",
    borderColor: "#d1d5db",
  };
};

interface HistoryCardProps {
  data: TradeRequest;
  onOpenReviewModal: (tradeId: number) => void;
}

// Enhanced Transaction Card Component matching web design
const HistoryCard = ({ data, onOpenReviewModal }: HistoryCardProps) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const statusConfig = getStatusConfig(data?.order_status || "");

  // Enhanced date formatting
  const formatDateTime = (dateString: string) => {
    if (!dateString) return { date: "N/A", time: "N/A", relative: "Unknown" };

    try {
      const date = new Date(dateString);
      const formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
      const formattedTime = date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });

      // Calculate relative time
      const now = new Date();
      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
      let relative;

      if (diffInHours < 1) {
        relative = "Just now";
      } else if (diffInHours < 24) {
        relative = `${diffInHours}h ago`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        relative = `${diffInDays}d ago`;
      }

      return { date: formattedDate, time: formattedTime, relative };
    } catch (error) {
      console.error("Date formatting error:", error);
      return {
        date: "Invalid date",
        time: "Invalid time",
        relative: "Unknown",
      };
    }
  };

  const { date: formattedDate, time: formattedTime, relative: relativeTime } =
    formatDateTime(data?.created_date);

  const formatCurrency = (amount: number, currency: string) => {
    if (!amount) return "N/A";
    return `${Number(amount).toLocaleString()} ${currency || ""}`;
  };

  // Action handlers
  const handlePeerDecisionToTradeAccept = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    const payload = { order_id: data?.order_number };

    try {
      showToastSuccess("Processing trade acceptance...");
      const response = await customFetchWithToken.post("/trade/accept-request/", payload);

      if (response.status === 200) {
        setTimeout(() => {
          router.push(`/trade/${data?.order_number}`);
        }, 400);
      } else {
        showToastError(response.data.message || "Failed to accept trade");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error accepting trade:", error);
      showToastError("Failed to accept trade. Please try again.");
      setIsProcessing(false);
    }
  };

  const handlePeerDecisionToTradeReject = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    const payload = { order_id: data?.order_number };

    try {
      showToastSuccess("Processing trade rejection...");
      const response = await customFetchWithToken.post("/trade/reject-request/", payload);

      if (response.status === 200) {
        setTimeout(() => {
          router.push(`/trade/${data?.order_number}`);
        }, 400);
      } else {
        showToastError(response.data.message || "Failed to reject trade");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error rejecting trade:", error);
      showToastError("Failed to reject trade. Please try again.");
      setIsProcessing(false);
    }
  };

  const handleTradeById = () => {
    if (isProcessing) return;
    setIsProcessing(true);

    setTimeout(() => {
      router.push(`/trade/${data?.order_number}`);
    }, 500);
  };

  return (
    <View
      className="bg-white rounded-2xl shadow-lg mb-6 border-2 overflow-hidden"
      style={{
        borderColor: statusConfig.borderColor,
        shadowColor: statusConfig.color,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 8,
      }}
    >
      {/* Status Accent Bar */}
      <View
        className="h-1.5 w-full"
        style={{ backgroundColor: statusConfig.borderColor }}
      />

      {/* Card Header */}
      <View
        className="flex-row justify-between items-start p-6 border-b-2"
        style={{
          backgroundColor: statusConfig.bgColor,
          borderBottomColor: statusConfig.borderColor + '30',
        }}
      >
        <View className="flex-1">
          <View className="flex-row items-center mb-2">
            <Text className="text-xs font-bold text-gray-500 uppercase tracking-wider mr-2">
              Order
            </Text>
            <View
              className="px-2 py-0.5 rounded-md"
              style={{ backgroundColor: statusConfig.borderColor + '20' }}
            >
              <Text
                className="text-xs font-bold uppercase"
                style={{ color: statusConfig.color }}
              >
                {data?.order_status}
              </Text>
            </View>
          </View>
          <Text className="text-xl font-black text-gray-900 mb-2">
            #{data?.order_number}
          </Text>
          <View className="flex-row items-center">
            <View
              className="px-3 py-1.5 rounded-full mr-2"
              style={{ backgroundColor: statusConfig.borderColor + '15' }}
            >
              <Text
                className="text-xs font-semibold"
                style={{ color: statusConfig.color }}
              >
                {relativeTime}
              </Text>
            </View>
            <Text className="text-xs text-gray-500">
              {formattedDate} • {formattedTime}
            </Text>
          </View>
        </View>

        <View className="items-end ml-4">
          <View
            className="flex-row items-center px-4 py-2.5 rounded-xl border-2 shadow-sm"
            style={{
              backgroundColor: statusConfig.bgColor,
              borderColor: statusConfig.borderColor,
            }}
          >
            <Text className="text-lg mr-2">{statusConfig.icon}</Text>
            <Text
              className="text-sm font-bold uppercase tracking-wide"
              style={{ color: statusConfig.color }}
            >
              {statusConfig.label}
            </Text>
          </View>
        </View>
      </View>

      {/* Card Content */}
      <View className="p-6">
        {/* Trade Amount Highlight */}
        <View
          className="p-5 rounded-2xl mb-6 border-2 shadow-sm"
          style={{
            backgroundColor: statusConfig.bgColor,
            borderColor: statusConfig.borderColor,
          }}
        >
          <View className="items-center">
            <Text className="text-xs font-bold uppercase tracking-wider mb-2" style={{ color: statusConfig.color }}>
              Trade Amount
            </Text>
            <View className="flex-row items-center justify-center mb-3">
              <View className="items-center">
                <Text className="text-2xl font-black" style={{ color: statusConfig.color }}>
                  {formatCurrency(data?.trade_amount, data?.payin_currency)}
                </Text>
                <Text className="text-xs font-semibold text-gray-600 mt-1">
                  You Pay
                </Text>
              </View>

              <View className="mx-4 items-center">
                <View
                  className="p-3 rounded-full"
                  style={{ backgroundColor: statusConfig.borderColor }}
                >
                  <Ionicons name="arrow-forward" size={20} color="white" />
                </View>
              </View>

              <View className="items-center">
                <Text className="text-2xl font-black" style={{ color: statusConfig.color }}>
                  {data?.payout_currency}
                </Text>
                <Text className="text-xs font-semibold text-gray-600 mt-1">
                  You Receive
                </Text>
              </View>
            </View>

            <View
              className="px-4 py-2 rounded-lg"
              style={{ backgroundColor: statusConfig.borderColor + '20' }}
            >
              <Text className="text-center text-sm font-semibold" style={{ color: statusConfig.color }}>
                Rate: {data?.indicative_fx_rate
                  ? `1 ${data?.payin_currency} = ${Number(data?.indicative_fx_rate).toFixed(4)} ${data?.payout_currency}`
                  : "Rate not available"}
              </Text>
            </View>
          </View>
        </View>

        {/* Trade Details Grid */}
        <View className="space-y-4">
          {/* Trade Details Section */}
          <View
            className="p-5 rounded-xl border"
            style={{
              backgroundColor: 'white',
              borderColor: statusConfig.borderColor + '30',
            }}
          >
            <View className="flex-row items-center mb-4">
              <View
                className="p-2 rounded-lg mr-3"
                style={{ backgroundColor: statusConfig.bgColor }}
              >
                <Text className="text-lg">💰</Text>
              </View>
              <Text className="text-base font-bold text-gray-800">Trade Information</Text>
            </View>
            <View className="space-y-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-sm font-medium text-gray-600">Listing ID</Text>
                <View
                  className="px-3 py-1 rounded-lg"
                  style={{ backgroundColor: statusConfig.borderColor + '15' }}
                >
                  <Text
                    className="text-sm font-bold"
                    style={{ color: statusConfig.color }}
                  >
                    #{data?.listing?.id}
                  </Text>
                </View>
              </View>
              {data?.listing?.final_trade_fee && (
                <View className="flex-row justify-between items-center">
                  <Text className="text-sm font-medium text-gray-600">Trade Fee</Text>
                  <Text className="text-sm font-bold text-gray-900">{data?.listing?.final_trade_fee}%</Text>
                </View>
              )}
            </View>
          </View>

          {/* Payment Details Section */}
          <View
            className="p-5 rounded-xl border"
            style={{
              backgroundColor: 'white',
              borderColor: statusConfig.borderColor + '30',
            }}
          >
            <View className="flex-row items-center mb-4">
              <View
                className="p-2 rounded-lg mr-3"
                style={{ backgroundColor: statusConfig.bgColor }}
              >
                <Text className="text-lg">💳</Text>
              </View>
              <Text className="text-base font-bold text-gray-800">Payment Methods</Text>
            </View>
            <View className="space-y-4">
              <View className="flex-row justify-between items-center">
                <View className="flex-1">
                  <Text className="text-sm font-medium text-gray-600 mb-1">Pay In Currency</Text>
                  <View
                    className="px-3 py-2 rounded-lg self-start"
                    style={{ backgroundColor: statusConfig.borderColor }}
                  >
                    <Text className="text-sm font-bold text-white">{data?.payin_currency}</Text>
                  </View>
                </View>
                <View className="flex-1 items-end">
                  <Text className="text-sm font-medium text-gray-600 mb-1">Method</Text>
                  <Text className="text-sm font-bold text-gray-900">{data?.payin_option}</Text>
                </View>
              </View>

              <View className="h-px bg-gray-200" />

              <View className="flex-row justify-between items-center">
                <View className="flex-1">
                  <Text className="text-sm font-medium text-gray-600 mb-1">Pay Out Currency</Text>
                  <View
                    className="px-3 py-2 rounded-lg self-start"
                    style={{ backgroundColor: statusConfig.borderColor }}
                  >
                    <Text className="text-sm font-bold text-white">{data?.payout_currency}</Text>
                  </View>
                </View>
                <View className="flex-1 items-end">
                  <Text className="text-sm font-medium text-gray-600 mb-1">Method</Text>
                  <Text className="text-sm font-bold text-gray-900">{data?.payout_option}</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View
        className="p-6 border-t-2"
        style={{
          backgroundColor: statusConfig.bgColor + '40',
          borderTopColor: statusConfig.borderColor + '30',
        }}
      >
        {data?.order_status?.toLowerCase() === "ongoing" && (
          <TouchableOpacity
            onPress={handleTradeById}
            disabled={isProcessing}
            className="py-4 px-6 rounded-xl flex-row items-center justify-center shadow-lg"
            style={{
              backgroundColor: statusConfig.borderColor,
              shadowColor: statusConfig.borderColor,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 8,
            }}
          >
            <Text className="text-lg mr-3">🔄</Text>
            <Text className="text-white font-bold text-base">
              {isProcessing ? "Loading..." : "Continue Trade"}
            </Text>
          </TouchableOpacity>
        )}

        {data?.order_status?.toLowerCase() === "pending" && (
          <View className="space-y-4">
            <View
              className="flex-row items-center justify-center p-3 rounded-xl"
              style={{ backgroundColor: statusConfig.borderColor + '20' }}
            >
              <Text className="text-lg mr-3">⏳</Text>
              <Text
                className="text-base font-bold"
                style={{ color: statusConfig.color }}
              >
                Trade Awaiting Response
              </Text>
            </View>
            <View className="flex-row space-x-3">
              <TouchableOpacity
                onPress={handlePeerDecisionToTradeAccept}
                disabled={isProcessing}
                className="flex-1 py-4 px-4 rounded-xl flex-row items-center justify-center shadow-lg"
                style={{
                  backgroundColor: '#10b981',
                  shadowColor: '#10b981',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 8,
                }}
              >
                <Text className="text-lg mr-2">✅</Text>
                <Text className="text-white font-bold">
                  {isProcessing ? "Processing..." : "Accept"}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handlePeerDecisionToTradeReject}
                disabled={isProcessing}
                className="flex-1 py-4 px-4 rounded-xl flex-row items-center justify-center border-2 shadow-lg"
                style={{
                  backgroundColor: 'white',
                  borderColor: '#ef4444',
                  shadowColor: '#ef4444',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.2,
                  shadowRadius: 8,
                  elevation: 8,
                }}
              >
                <Text className="text-lg mr-2">❌</Text>
                <Text className="text-red-500 font-bold">
                  {isProcessing ? "Processing..." : "Reject"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {data?.order_status?.toLowerCase() === "completed" && (
          <View className="space-y-4">
            <View
              className="flex-row items-center justify-center p-4 rounded-xl"
              style={{ backgroundColor: '#d1fae5' }}
            >
              <Text className="text-lg mr-3">✅</Text>
              <Text className="text-base font-bold text-green-800">
                Trade Completed Successfully
              </Text>
            </View>
            <TouchableOpacity
              onPress={handleTradeById}
              disabled={isProcessing}
              className="py-4 px-6 rounded-xl flex-row items-center justify-center border-2 shadow-lg"
              style={{
                backgroundColor: 'white',
                borderColor: statusConfig.borderColor,
                shadowColor: statusConfig.borderColor,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 8,
                elevation: 8,
              }}
            >
              <Text className="text-lg mr-3">👁️</Text>
              <Text
                className="font-bold text-base"
                style={{ color: statusConfig.color }}
              >
                View Details
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {(data?.order_status?.toLowerCase() === "rejected" ||
          data?.order_status?.toLowerCase() === "expired" ||
          data?.order_status?.toLowerCase() === "cancelled") && (
          <View
            className="flex-row items-center justify-center p-4 rounded-xl"
            style={{ backgroundColor: statusConfig.bgColor }}
          >
            <Text className="text-lg mr-3">
              {data?.order_status?.toLowerCase() === "rejected"
                ? "❌"
                : data?.order_status?.toLowerCase() === "expired"
                ? "⏰"
                : "🚫"}
            </Text>
            <Text
              className="text-base font-bold"
              style={{ color: statusConfig.color }}
            >
              Trade {data?.order_status?.charAt(0).toUpperCase() + data?.order_status?.slice(1)}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

export default HistoryCard;