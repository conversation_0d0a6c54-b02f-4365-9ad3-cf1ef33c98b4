import { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";

import customFetchWithToken from "@/app/utils/axiosInterceptor";
import { Toast } from "react-native-toast-message/lib/src/Toast";
import ModifyModal from "./ModifyWithdrawModal";
import { Feather, MaterialIcons } from "@expo/vector-icons";

interface WithdrawHistoryItemProps {
  id: string;
  amount: number;
  address: string;
  status: string;
  setEditSuccess: (value: boolean) => void;
}

const WithdrawHistoryItem = ({
  id,
  amount,
  address,
  status,
  setEditSuccess,
}: WithdrawHistoryItemProps) => {
  const [showModal, setShowModal] = useState(false);

  const handleDeleteWithdrawlReq = async () => {
    try {
      const res = await customFetchWithToken.delete(
        `/delete-withdraw-request/${id}`
      );
      console.log("resD", res);
      setEditSuccess(true);
      Toast.show({
        type: "success",
        text1: res.data.message,
      });
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <View className="bg-gray-200 p-4 rounded-lg mb-4 shadow-sm">
      <ModifyModal
        id={id}
        showModal={showModal}
        setShowModal={setShowModal}
        amount={amount}
        address={address}
        setEditSuccess={setEditSuccess}
      />

      <View className="flex-row justify-between items-center mb-2 overflow-y-scroll">
        <Text className="text-gray-800 text-base">Amount: {amount}</Text>
        <View className="flex-row space-x-4">
          <TouchableOpacity
            onPress={() => setShowModal(!showModal)}
            className="p-2"
          >
            <Feather name="edit" size={20} color="#4B5563" />
          </TouchableOpacity>

          <TouchableOpacity onPress={handleDeleteWithdrawlReq} className="p-2">
            <MaterialIcons name="delete" size={20} color="#4B5563" />
          </TouchableOpacity>
        </View>
      </View>

      <View className="mb-2">
        <Text className="text-gray-800">
          Status: <Text className="text-blue-500">{status}</Text>
        </Text>
      </View>

      <Text className="text-gray-800">
        External Wallet Address: {address.slice(0, 8)}...{address.slice(-5)}
      </Text>
    </View>
  );
};

export default WithdrawHistoryItem;
