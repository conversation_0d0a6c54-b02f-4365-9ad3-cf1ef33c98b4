import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Picker } from "@react-native-picker/picker";
import "nativewind"; // Ensure that NativeWind is imported
import Layout from "../components/Layout";
import customFetchWithToken from "./utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import { useRouter } from "expo-router";

interface Currency {
  currency_code: string;
  currency_name: string;
  currency_type: "fiat" | "crypto";
  id: number;
  priority: number;
}
interface PaymentMethodTypes {
  country_name: string;
  id: Number;
  payment_method: string;
}

interface AccountData {
  key: string;
  value: string;
}
interface Account {
  id: number;
  payment_method: string;
  data: AccountData[];
}

export default function EditListingForm() {
  const router = useRouter();
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };
  const showToast = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };

  const [liquidityAvailable, setLiquidityAvailable] = useState("");
  const [minTradeLimit, setMinTradeLimit] = useState("");
  const [maxTradeLimit, setMaxTradeLimit] = useState("");
  const [indicativeFxRate, setIndicativeFxRate] = useState(
    "select currencies for rate"
  );
  const [tradeFeePercentage, setTradeFeePercentage] = useState("");
  const [rate, setRate] = useState("");
  const [termsAndConditions, setTermsAndConditions] = useState("");
  const [currencyAccepted, setCurrencyAccepted] = useState("");
  const [currencyPayout, setCurrencyPayout] = useState("");
  const [loadCurrencyAccepted, setLoadCurrencyAccepted] = useState<Currency[]>(
    []
  );
  const [loadCurrencyPayout, setLoadCurrencyPayout] = useState<Currency[]>([]);
  const [loadPaymentAccepted, setLoadPaymentAccepted] = useState<
    PaymentMethodTypes[]
  >([]);
  const [loadPaymentPayout, setLoadPaymentPayout] = useState<
    PaymentMethodTypes[]
  >([]);
  const [paymentMethodAccepted, setPaymentMethodAccepted] = useState("");
  const [paymentMethodPayout, setPaymentMethodPayout] = useState("");
  const [availableAccounts, setAvailableAccounts] = useState<Account[]>([]);
  const [availableAccDataMessage, setAvailableAccDataMessage] = useState("");

  const [selectedAccount, setSelectedAccount] = useState(""); // Or set a default value
  const [tradeTimeLimit, setTradeTimeLimit] = useState("");
  console.log("availableAccDataMessage", availableAccDataMessage);
  const Data = {
    currency_accepted: currencyAccepted,
    currency_payout: currencyPayout,
    available_liquidity: Number(liquidityAvailable),
    terms_and_conditions: termsAndConditions,
    min_liquidity: Number(minTradeLimit),
    max_liquidity: Number(maxTradeLimit),
    indicative_fx_rate: Number(indicativeFxRate),
    payin_option: paymentMethodAccepted,
    payout_option: paymentMethodPayout,
    trade_fee: Number(tradeFeePercentage),
    user_payment_option: selectedAccount,
    time_limit: Number(tradeTimeLimit),
  };

  const addListingHandler = async () => {
    if (currencyAccepted.length == 0) {
      return showToast("Please enter accepted currencies");
    }

    if (currencyPayout.length == 0) {
      return showToast("Please enter payout currencies");
    }

    if (liquidityAvailable.length == 0) {
      return showToast("Please enter a liquidity value");
    }

    if (minTradeLimit.length == 0) {
      return showToast("Please enter a minimum trade limit");
    }

    if (maxTradeLimit.length == 0) {
      return showToast("Please enter a maximum trade limit");
    }

    // Add new validation checks
    const minLiquidityNum = Number(minTradeLimit);
    const maxLiquidityNum = Number(maxTradeLimit);
    const availableLiquidityNum = Number(liquidityAvailable);

    if (minLiquidityNum >= maxLiquidityNum) {
      return showToast(
        "Minimum trade limit must be less than maximum trade limit"
      );
    }

    if (minLiquidityNum > availableLiquidityNum) {
      return showToast("Minimum trade limit cannot exceed available liquidity");
    }

    if (maxLiquidityNum > availableLiquidityNum) {
      return showToast("Maximum trade limit cannot exceed available liquidity");
    }

    if (rate.length == 0) {
      return showToast("Please enter a final trade fee");
    }

    if (tradeFeePercentage.length == 0) {
      return showToast("Please enter a trade fee");
    }
    if (tradeTimeLimit == null) {
      return showToast("Please enter a selected time limit");
    }

    if (termsAndConditions.length == 0) {
      return showToast("Please enter the terms and conditions");
    }

    try {
      const res = await customFetchWithToken.post("/add-listings/", Data);

      if (res.status === 200 || res.status === 201) {
        showToast(res.data.message);
        res.data.message;
        setLiquidityAvailable("");
        setMinTradeLimit("");
        setMaxTradeLimit("");
        setIndicativeFxRate("");
        setTradeFeePercentage("");
        setRate("");
        setTermsAndConditions("");
        setCurrencyAccepted("");
        setCurrencyPayout("");
   
        setLoadPaymentAccepted([]);
        setLoadPaymentPayout([]);
        setPaymentMethodAccepted("");
        setPaymentMethodPayout("");
        setAvailableAccounts([]);
        setSelectedAccount("");
        setTradeTimeLimit("");
      } else {
        showToast("Add Listing Failed.");
      }
    } catch (error: any) {
      showToast(error.response.data.message);
      console.log(error);
    }
  };

  const fetchAcceptedCurrency = async () => {
    try {
      const res = await customFetchWithToken(
        "/currency-list/?currency_from=true"
      );

      setLoadCurrencyAccepted(res.data.data);
    } catch (error: any) {
      console.log(error.response.data);
    }
  };
  const fetchpayoutCurrency = async () => {
    try {
      const res = await customFetchWithToken(
        "/currency-list/?currency_to=true"
      );

      setLoadCurrencyPayout(res.data.data);
    } catch (error: any) {
      console.log(error.response.data);
    }
  };

  const fetchPaymentMethodsAccepted = async () => {
    try {
      const resCurrency = await customFetchWithToken(
        `/payment-list/?currency=${currencyAccepted}`
      );

      setLoadPaymentAccepted(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };
  const fetchPaymentMethodsPayout = async () => {
    try {
      const resCurrency = await customFetchWithToken(
        `/payment-list/?currency=${currencyPayout}`
      );

      setLoadPaymentPayout(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchAvailableAccounts = async () => {
    try {
      const res = await customFetchWithToken(
        `/get-user-choice-payment-fields-data/?payment_method=${paymentMethodAccepted}`
      );
    } catch (error) {
      console.log(error);
    }
  };

  const fetchFxRate = async () => {
    if (!currencyAccepted || !currencyPayout) {
      setIndicativeFxRate("select currencies for rate");
      return;
    }

    try {
      const resFx = await customFetchWithToken(
        `currency-converter/?from_currency=${currencyAccepted}&to_currency=${currencyPayout}`
      );

      const statusCode = resFx?.data.status;

      setIndicativeFxRate(resFx?.data.data.rate);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchFinalTradeFee = async () => {
    if (!tradeFeePercentage || !indicativeFxRate) {
      return;
    }
    try {
      const res = await customFetchWithToken(
        `/final-exchange-rate/?trade_fee=${tradeFeePercentage}&indicative_fx_rate=${indicativeFxRate}`
      );

      if (res.data.status !== 200) {
        setRate("enter trade fee % for rate");
      } else setRate(res.data.data.final_exchange_rate);
    } catch (error) {
      console.log(error);
    }
  };

  const getAvailableAccounts = async () => {
    if (paymentMethodAccepted.length < 1) {
      return null;
    }
    try {
      const res = await customFetchWithToken.get(
        `/get-user-choice-payment-fields-data/?payment_method=${paymentMethodAccepted}`
      );

      setAvailableAccounts(res.data.data);
      setAvailableAccDataMessage(res.data.message);
    } catch (error: any) {
      console.log(error);
      setAvailableAccDataMessage(error.response.data.message);
    }
  };

  const handleSelectedSavedAcc = (value: string) => {
    console.log("Selected Account", value);
    if (value === "no-accounts") {
      router.push("/accounts");
      return;
    }
    setSelectedAccount(value);
  };

  useEffect(() => {
    getAvailableAccounts();
  }, [paymentMethodAccepted]);

  useEffect(() => {
    fetchFinalTradeFee();
  }, [tradeFeePercentage]);

  useEffect(() => {
    fetchAcceptedCurrency();
    fetchpayoutCurrency();
  }, []);

  useEffect(() => {
    fetchFxRate();
  }, [currencyAccepted, currencyPayout]);

  useEffect(() => {
    if (currencyAccepted.length > 0) {
      fetchPaymentMethodsAccepted();
    }
  }, [currencyAccepted]);
  useEffect(() => {
    if (currencyPayout.length > 0) {
      fetchPaymentMethodsPayout();
    }
  }, [currencyPayout]);
  useEffect(() => {
    if (paymentMethodAccepted.length > 0) {
      fetchAvailableAccounts();
    }
  }, [paymentMethodAccepted]);

  const handleSubmit = () => {
    // Handle form submission here
  };

  return (
    <Layout>
      <ScrollView className="flex-1 p-4 bg-gray-100">
        <View className="flex-row justify-between items-center mb-5">
          <Text className="text-2xl font-pbold">Add Listing</Text>
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">Currency accepted</Text>
          <View className="border border-gray-300 rounded-md bg-white">
            <Picker
              selectedValue={currencyAccepted}
              onValueChange={(itemValue) => setCurrencyAccepted(itemValue)}
              className="bg-white font-pmedium"
            >
              <Picker.Item label="Select a Currency" value="" />
              {loadCurrencyAccepted?.map((el, index) => (
                <Picker.Item
                  key={index}
                  label={el.currency_code}
                  value={el.currency_code}
                />
              ))}
            </Picker>
          </View>
        </View>

        <View className="mb-4 font-pmedium">
          <Text className="text-base mb-2 font-pmedium">
            Currency of payout
          </Text>
          <View className="border border-gray-300 rounded-md bg-white ">
            <Picker
              selectedValue={currencyPayout}
              onValueChange={(itemValue) => setCurrencyPayout(itemValue)}
              className="bg-white"
            >
              <Picker.Item label="Select a Currency" value="" />
              {loadCurrencyPayout?.map((el, index) => (
                <Picker.Item
                  key={index}
                  label={el.currency_code}
                  value={el.currency_code}
                />
              ))}
            </Picker>
          </View>
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">
            Payment Method Accepted
          </Text>

          <View className="border border-gray-300 rounded-md bg-white">
            <Picker
              selectedValue={paymentMethodAccepted}
              onValueChange={(itemValue) => setPaymentMethodAccepted(itemValue)}
              className="bg-white"
            >
              <Picker.Item label="Select a Accepted Currency First" value="" />
              {loadPaymentAccepted?.map((el, index) => (
                <Picker.Item
                  key={index}
                  label={el.payment_method}
                  value={el.payment_method}
                />
              ))}
            </Picker>
          </View>
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">
            Payment Method Payout
          </Text>
          <View className="border border-gray-300 rounded-md bg-white">
            <Picker
              selectedValue={paymentMethodPayout}
              onValueChange={(itemValue) => setPaymentMethodPayout(itemValue)}
              className="bg-white"
            >
              <Picker.Item label="Select a Payout Currency First" value="" />
              {loadPaymentPayout?.map((el, index) => (
                <Picker.Item
                  key={index}
                  label={el.payment_method}
                  value={el.payment_method}
                />
              ))}
            </Picker>
          </View>
        </View>

        <View className="mb-4 h-auto">
          <Text className="text-base mb-2 font-pmedium">
            Liquidity Available
          </Text>
          <TextInput
            placeholder="Enter Available Liquidity"
            className="border border-gray-300 rounded-md bg-white p-2  h-14"
            value={liquidityAvailable}
            maxLength={10}
            onChangeText={(text) => {
              // Filter out non-numeric characters
              const numericText = text.replace(/[^0-9]/g, "");
              setLiquidityAvailable(numericText);
            }}
            keyboardType="numeric"
          />
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">
            Minimum trade limit
          </Text>
          <TextInput
            placeholder="Enter Minimum Liquidity"
            className="border border-gray-300 rounded-md bg-white p-2 h-14"
            value={minTradeLimit}
            maxLength={10}
            onChangeText={(text) => {
              // Filter out non-numeric characters
              const numericText = text.replace(/[^0-9]/g, "");
              setMinTradeLimit(numericText);
            }}
            keyboardType="numeric"
          />
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">
            Maximum trade limit
          </Text>
          <TextInput
            placeholder="Enter Maximum Liquidity"
            className="border border-gray-300 rounded-md bg-white p-2 h-14"
            value={maxTradeLimit}
            maxLength={10}
            onChangeText={(text) => {
              // Filter out non-numeric characters
              const numericText = text.replace(/[^0-9]/g, "");
              setMaxTradeLimit(numericText);
            }}
            keyboardType="numeric"
          />
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">Official FX Rate</Text>
          <TextInput
            className="border border-gray-300 rounded-md bg-white p-2 h-14 text-black"
            value={
              indicativeFxRate !== "select currencies for rate"
                ? parseFloat(indicativeFxRate).toFixed(3)
                : indicativeFxRate
            }
            readOnly
          />
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">% listing margin</Text>
          <TextInput
            className="border border-gray-300 rounded-md bg-white p-2 h-14"
            value={tradeFeePercentage.toString()}
            maxLength={10}
            onChangeText={(text) => {
              // Allow numeric characters, a single decimal point, and a leading negative sign
              const numericText = text.replace(/[^0-9.-]/g, "");
              const formattedText =
                numericText.split(".").length > 2 ||
                numericText.split("-").length > 2
                  ? numericText.slice(0, -1) // Remove extra decimal point or negative sign if more than one is found
                  : numericText;
              setTradeFeePercentage(formattedText);
            }}
            placeholder="Enter trade fee percentage"
            keyboardType="numeric"
          />
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">Final Rate</Text>
          <TextInput
            className="border text-black border-gray-300 rounded-md bg-white p-2 h-14"
            value={rate.toString()}
            readOnly
          />
        </View>

        {currencyAccepted !== "USDT" ? (
          <View className="mb-4">
            <Text className="text-base mb-2 font-pmedium">
              Available Accounts
            </Text>
            <View className="border border-gray-300 rounded-md bg-white">
              <Picker
                selectedValue={selectedAccount} // Use a state variable for the selected value
                onValueChange={(itemValue) =>
                  handleSelectedSavedAcc(itemValue as string)
                }
                // onValueChange={(itemValue) => setSelectedAccount(itemValue)}
                className="bg-white"
              >
                {!currencyAccepted && (
                  <Picker.Item label="Select a Currency Accepted" value="" />
                )}

                {availableAccDataMessage === "Data found." && (
                  <Picker.Item
                    label="Select available Payment Method"
                    value="-1"
                  />
                )}
                {availableAccDataMessage !== "Data found." && (
                  <Picker.Item
                    label="Go to Saved Accounts page to Create an account to
                            Complete the Listing"
                    value="no-accounts"
                  />
                )}

                {availableAccounts.length > 0 &&
                  availableAccounts.map((item, index) => (
                    <Picker.Item
                      key={index}
                      label={`${item.data[0].key} - ${item.data[0].value}`} // Proper template literal syntax
                      value={item.id}
                    />
                  ))}
              </Picker>
            </View>
          </View>
        ) : (
          ""
        )}

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">
            Set Trade Time Limit
          </Text>
          <View className="border border-gray-300 rounded-md bg-white">
            <Picker
              selectedValue={tradeTimeLimit}
              onValueChange={(itemValue) => setTradeTimeLimit(itemValue)}
              className="bg-white"
            >
              <Picker.Item label="Select Time Limit" value="" />
              <Picker.Item label="15 minutes" value="15" />
              <Picker.Item label="30 minutes" value="30" />
              <Picker.Item label="45 minutes" value="45" />
              <Picker.Item label="60 minutes" value="60" />
            </Picker>
          </View>
        </View>

        <View className="mb-4">
          <Text className="text-base mb-2 font-pmedium">
            Terms and conditions (max 200 characters)
          </Text>
          <TextInput
            className="border border-gray-300 rounded-md p-2 bg-white h-24 font-pmedium"
            value={termsAndConditions}
            onChangeText={(inputText) => setTermsAndConditions(inputText)}
            multiline
            numberOfLines={4}
            maxLength={200}
            placeholder="Enter terms and conditions"
          />
        </View>

        <TouchableOpacity
          className="bg-[#4153ed] p-4 rounded-md items-center mt-4 mb-10"
          onPress={addListingHandler}
        >
          <Text className="text-white text-lg font-pbold">Add Listing</Text>
        </TouchableOpacity>
      </ScrollView>
      <Toast config={toastConfig} />
    </Layout>
  );
}