import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import Toast, { BaseToast } from "react-native-toast-message";
import { useRouter } from "expo-router";
import customFetchWithToken from "./utils/axiosInterceptor";
import { Ionicons } from "@expo/vector-icons";

interface currency {
  currency_code: string;
  currency_name: string;
  currency_type: string;
  id: number;
}
interface country {
  country_name: string;
  country_short_name: string;
  flag: string;
  phone_code: string;
}

export default function USDTQuestionnaire() {
  const router = useRouter();
  const [responses, setResponses] = useState({
    buyOrSell: "",
    localCurrency: "",
    tradingVolume: "",
    country: "",
  });
  const [currencyOptions, setCurrencyOptions] = useState<currency[]>([]);
  const [countryOptions, setCountryOptions] = useState<country[]>([]);
  const [loading, setLoading] = useState(false);
  console.log("countryOptions", currencyOptions);
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }}
        text1Style={{ fontSize: 14, fontWeight: "bold" }}
        text2Style={{ fontSize: 12 }}
      />
    ),
    error: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "red", width: "90%" }}
        text1Style={{ fontSize: 14, fontWeight: "bold" }}
        text2Style={{ fontSize: 12 }}
      />
    ),
  };

  const showToast = (type: "success" | "error", message: string) => {
    Toast.show({
      type,
      text1: message,
      position: "top",
      visibilityTime: 4000,
    });
  };

  const handleInputChange = (name: string, value: string) => {
    setResponses((prev) => ({ ...prev, [name]: value }));
  };

  const fetchCurrencies = async () => {
    try {
      const res = await customFetchWithToken.get("/currency-list/");
      setCurrencyOptions(res.data.data);
    } catch (error) {
      console.error("Error fetching currencies:", error);
      showToast("error", "Failed to load currency options.");
    }
  };

  const fetchCountries = async () => {
    try {
      const res = await customFetchWithToken.get("/country-code/");
      setCountryOptions(res.data.data);
    } catch (error) {
      console.error("Error fetching countries:", error);
      showToast("error", "Failed to load country options.");
    }
  };

  const validateForm = () => {
    if (!responses.buyOrSell) {
      showToast("error", "Please select whether you want to buy or sell USDT.");
      return false;
    }
    if (!responses.localCurrency) {
      showToast("error", "Please select your local currency.");
      return false;
    }
    if (!responses.tradingVolume || Number(responses.tradingVolume) <= 0) {
      showToast("error", "Please enter a valid trading volume greater than 0.");
      return false;
    }
    if (!responses.country) {
      showToast("error", "Please select your country.");
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const payload = {
      buy_or_sell: responses.buyOrSell,
      currency: responses.localCurrency,
      trading_volume: responses.tradingVolume,
      country: responses.country,
    };

    setLoading(true);

    try {
      const res = await customFetchWithToken.post(
        "/transaction-view/",
        payload
      );
      if (res.status === 200) {
        showToast("success", "Response submitted successfully.");
        setTimeout(() => router.push("/searchads"), 1500);
      } else {
        showToast("error", "Submission failed. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting response:", error);
      showToast("error", "Failed to submit the response.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCurrencies();
    fetchCountries();
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView contentContainerStyle={{ padding: 20 }}>
        <View className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            USDT Questionnaire
          </Text>
          <Text className="text-gray-500 mb-6">
            Please fill out the following information to help us better serve
            you
          </Text>

          <View className="space-y-6">
            <View>
              <Text className="text-gray-700 font-semibold mb-2">
                Buy or Sell USDT
              </Text>
              <View className="border border-gray-200 rounded-lg bg-white">
                <Picker
                  selectedValue={responses.buyOrSell}
                  onValueChange={(value) =>
                    handleInputChange("buyOrSell", value)
                  }
                  className="h-12"
                  dropdownIconColor="#4B5563"
                >
                  <Picker.Item
                    label="Select an option"
                    value=""
                    color="#9CA3AF"
                  />
                  <Picker.Item label="Buy" value="buy" color="#1F2937" />
                  <Picker.Item label="Sell" value="sell" color="#1F2937" />
                </Picker>
              </View>
            </View>

            <View>
              <Text className="text-gray-700 font-semibold mb-2">
                Local Currency
              </Text>
              <View className="border border-gray-200 rounded-lg bg-white">
                <Picker
                  selectedValue={responses.localCurrency}
                  onValueChange={(value) =>
                    handleInputChange("localCurrency", value)
                  }
                  className="h-12"
                  dropdownIconColor="#4B5563"
                >
                  <Picker.Item
                    label="Select a currency"
                    value=""
                    color="#9CA3AF"
                  />
                  {currencyOptions.map((currency, index: number) => (
                    <Picker.Item
                      key={index}
                      label={currency.currency_code}
                      value={currency.currency_code}
                      color="#1F2937"
                    />
                  ))}
                </Picker>
              </View>
            </View>

            <View>
              <Text className="text-gray-700 font-semibold mb-2">
                Expected Monthly Trading Volume (in $)
              </Text>
              <View className="border border-gray-200 rounded-lg bg-white flex-row items-center px-3">
                <Ionicons name="cash-outline" size={20} color="#6B7280" />
                <TextInput
                  className="flex-1 p-3 text-gray-800"
                  keyboardType="numeric"
                  value={responses.tradingVolume}
                  onChangeText={(value) =>
                    handleInputChange("tradingVolume", value)
                  }
                  placeholder="Enter trading volume"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>

            <View>
              <Text className="text-gray-700 font-semibold mb-2">Country</Text>
              <View className="border border-gray-200 rounded-lg bg-white">
                <Picker
                  selectedValue={responses.country}
                  onValueChange={(value) => handleInputChange("country", value)}
                  className="h-12"
                  dropdownIconColor="#4B5563"
                >
                  <Picker.Item
                    label="Select your country"
                    value=""
                    color="#9CA3AF"
                  />
                  {countryOptions.map((country, index) => (
                    <Picker.Item
                      key={index}
                      label={country.country_name}
                      value={country.country_name}
                      color="#1F2937"
                    />
                  ))}
                </Picker>
              </View>
            </View>
          </View>
        </View>

        <TouchableOpacity
          className="bg-blue-600 py-4 rounded-lg items-center shadow-sm mb-8"
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text className="text-white font-bold text-lg">
              Submit Responses
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
      <Toast config={toastConfig} />
    </SafeAreaView>
  );
}
