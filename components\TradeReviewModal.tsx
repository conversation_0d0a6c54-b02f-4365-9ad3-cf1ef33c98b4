import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Pressable,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import customFetchWithToken from "../app/utils/axiosInterceptor";

interface FeedbackQuestion {
  id: number;
  question_name: string;
  option: {
    name: string;
    value: string;
    options?: string[];
  };
  is_deleted: boolean;
}

interface TradeReviewModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: (rating: number, comment: string) => void;
  tradeOrderId: string; // To identify the trade being reviewed
}

// Custom rating component that doesn't use defaultProps
const SimpleRating = ({
  rating,
  onRating,
}: {
  rating: number;
  onRating: (value: number) => void;
}) => {
  const stars = [1, 2, 3, 4, 5];
  const reviews = ["Terrible", "Bad", "Okay", "Good", "Great"];

  return (
    <View style={styles.ratingContainer}>
      <View style={styles.starsContainer}>
        {stars.map((star) => (
          <TouchableOpacity key={star} onPress={() => onRating(star)}>
            <Ionicons
              name={star <= rating ? "star" : "star-outline"}
              size={30}
              color={star <= rating ? "#FFD700" : "#CCCCCC"}
              style={styles.star}
            />
          </TouchableOpacity>
        ))}
      </View>
      <Text style={styles.ratingText}>{reviews[rating - 1]}</Text>
    </View>
  );
};

const TradeReviewModal: React.FC<TradeReviewModalProps> = ({
  isVisible,
  onClose,
  onSubmit,
  tradeOrderId,
}) => {
  const [rating, setRating] = useState(3); // Default rating
  const [comment, setComment] = useState("");
  const [questions, setQuestions] = useState<FeedbackQuestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch feedback questions when modal becomes visible
    if (isVisible) {
      fetchFeedbackQuestions();
    }
  }, [isVisible]);

  const fetchFeedbackQuestions = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await customFetchWithToken.get(
        "/get-base-feedback-question/"
      );
      if (res?.data?.results) {
        setQuestions(res.data.results);
        // Initialize rating based on the first question if it's a rating type
        const ratingQuestion = res.data.results.find(
          (q: FeedbackQuestion) =>
            q.option.value === "select" && q.option.options?.includes("5")
        );
        if (ratingQuestion) {
          setRating(3); // Default to middle rating
        }
      }
    } catch (error: any) {
      console.error("Error fetching feedback questions:", error);
      setError("Failed to load feedback questions");
    } finally {
      setLoading(false);
    }
  };

  const handleRating = (value: number) => {
    setRating(value);
  };

  const handleSubmit = () => {
    // First send the review data to the backend
    submitReviewToBackend(rating, comment)
      .then(() => {
        // Then call the parent component's onSubmit callback
        onSubmit(rating, comment);
        // Reset state
        setRating(3);
        setComment("");
        onClose(); // Close the modal after submitting
      })
      .catch((error) => {
        console.error("Error submitting review:", error);
        // You might want to show an error message to the user here
      });
  };

  const submitReviewToBackend = async (rating: number, comment: string) => {
    try {
      // Find the question IDs for the rating and comment
      const ratingQuestionId = ratingQuestion?.id;
      const commentQuestionId = commentQuestion?.id;

      if (!ratingQuestionId) {
        throw new Error("Rating question not found");
      }

      // Prepare the review data
      const reviewData = {
        trade_id: tradeOrderId,
        feedback: [
          {
            question_id: ratingQuestionId,
            answer: rating.toString(),
          },
        ],
      };

      // Add comment if provided and comment question exists
      if (comment && commentQuestionId) {
        reviewData.feedback.push({
          question_id: commentQuestionId,
          answer: comment,
        });
      }

      // Send the review data to the backend
      await customFetchWithToken.post("/submit-trade-feedback/", reviewData);
      return true;
    } catch (error) {
      console.error("Error submitting review to backend:", error);
      throw error;
    }
  };

  // Find rating and comment questions
  const ratingQuestion = questions.find(
    (q) => q.option.value === "select" && q.option.options?.includes("5")
  );
  const commentQuestion = questions.find((q) => q.option.value === "text");

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <Pressable style={styles.modalOverlay} onPress={onClose}>
        <View style={styles.content} onStartShouldSetResponder={() => true}>
          <Text style={styles.title}>Rate Your Trade Experience</Text>
          <Text style={styles.orderIdText}>Order ID: {tradeOrderId}</Text>

          {loading ? (
            <ActivityIndicator
              size="large"
              color="#4153ed"
              style={styles.loadingIndicator}
            />
          ) : error ? (
            <Text style={styles.errorText}>{error}</Text>
          ) : (
            <>
              {ratingQuestion && (
                <>
                  <Text style={styles.questionText}>
                    {ratingQuestion.question_name}
                  </Text>
                  <SimpleRating rating={rating} onRating={handleRating} />
                </>
              )}

              {commentQuestion && (
                <>
                  <Text style={styles.questionText}>
                    {commentQuestion.question_name}
                  </Text>
                  <TextInput
                    style={styles.textInput}
                    placeholder="Add your comments here"
                    value={comment}
                    onChangeText={setComment}
                    multiline
                  />
                </>
              )}
            </>
          )}

          <TouchableOpacity
            style={[
              styles.submitButton,
              (loading || error) && styles.disabledButton,
            ]}
            onPress={handleSubmit}
            disabled={loading || !!error}
          >
            <Text style={styles.submitButtonText}>Submit Review</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  content: {
    backgroundColor: "white",
    padding: 22,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: "rgba(0, 0, 0, 0.1)",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 12,
    textAlign: "center",
  },
  orderIdText: {
    fontSize: 14,
    color: "gray",
    textAlign: "center",
    marginBottom: 15,
  },
  questionText: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 10,
    textAlign: "center",
  },
  ratingContainer: {
    marginBottom: 20,
    alignItems: "center",
  },
  starsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
  },
  star: {
    marginHorizontal: 5,
  },
  ratingText: {
    fontSize: 16,
    color: "#666",
    marginTop: 5,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 5,
    padding: 10,
    minHeight: 80,
    textAlignVertical: "top", // Align text to the top for multiline
    marginBottom: 20,
  },
  submitButton: {
    backgroundColor: "#4153ed", // Example blue color
    padding: 15,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 10,
  },
  disabledButton: {
    backgroundColor: "#a0a0a0",
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  closeButton: {
    padding: 10,
    alignItems: "center",
  },
  closeButtonText: {
    color: "gray",
    fontSize: 14,
  },
  loadingIndicator: {
    marginVertical: 20,
  },
  errorText: {
    color: "red",
    textAlign: "center",
    marginVertical: 20,
  },
});

export default TradeReviewModal;
