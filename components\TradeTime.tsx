import { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";

import { useWebsocketContext } from "@/app/context/AuthContext";
import Toast from "react-native-toast-message";

interface TradeTimerProps {
  duration: number;
  orderNumber: string;
}

const TradeTimer = ({ duration, orderNumber }: TradeTimerProps) => {
  const { connection, sendMessage } = useWebsocketContext();
  const [time, setTime] = useState(duration || 0);
  const [stopTimer, setStopTimer] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [timerIncreased, setTimerIncreased] = useState(false);
  // console.log("duration", duration);
  const handleTimeIncrease = () => {
    const payload = {
      action: "increase_trade_time",
      order_id: orderNumber,
    };
    sendMessage(JSON.stringify(payload));
    setShowConfirmation(false);
    setTimerIncreased(true);
  };

  const handleTimeReject = () => {
    setShowConfirmation(false);
  };

  const getFormattedTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const minutes = totalMinutes % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  useEffect(() => {
    if (!duration || stopTimer) return;

    setTime(duration);
    const timer = setInterval(() => {
      setTime((prev) => {
        const newTime = prev - 1000;

        if (newTime === 60000 && !timerIncreased) {
          setShowConfirmation(true);
        }

        if (newTime <= 0) {
          clearInterval(timer);
          setStopTimer(true);
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [duration, stopTimer, timerIncreased]);

  return (
    <View className="flex-row justify-between items-center bg-white mt-4 h-4 rounded-lg">
      <View className="items-center justify-center bg-gray-100  rounded-lg">
        <Text className="text-md font-bold">
          {!duration ? "expired" : getFormattedTime(time)}
        </Text>
      </View>

      <Modal
        visible={showConfirmation}
        transparent={false}
        animationType="slide"
      >
        <View className="bg-white rounded-t-3xl p-6">
          <Text className="text-lg text-center mb-6">
            Your order {orderNumber}: Do you want to increase trade time by 10
            minutes?
          </Text>
          <View className="flex-row justify-around">
            <TouchableOpacity
              className="bg-green-500 px-8 py-3 rounded-lg"
              onPress={handleTimeIncrease}
            >
              <Text className="text-white font-bold text-center">Yes</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="bg-red-500 px-8 py-3 rounded-lg"
              onPress={handleTimeReject}
            >
              <Text className="text-white font-bold text-center">No</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Toast />
    </View>
  );
};

export default TradeTimer;
