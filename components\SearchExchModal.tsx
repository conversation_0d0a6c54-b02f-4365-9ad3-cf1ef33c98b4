import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
  Button,
  ScrollView,
  Modal,
  Pressable,
} from "react-native";

import { MaterialIcons } from "@expo/vector-icons";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import { debounce } from "lodash";
import { useWebsocketContext } from "@/app/context/AuthContext";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";
import Toast, { BaseToast } from "react-native-toast-message";
import { useRouter } from "expo-router";

interface currencyAccepted {
  currency_code: string;
  currency_name: string;
  id: number;
}
interface SearchExchModalProps {
  rate: Number;
  amount: string;
  available_liquidity: number;
  min_liquidity: number;
  max_liquidity: number;
  ListedOn: string;
  timeLimit: number;
  payin_option: string;
  payout_option: string;
  isVisible: boolean;
  terms: string;
  payInCurrency: string;
  payOutCurrency: string;
  showExchModal: boolean;
  listingId: number;
  onClose: () => void;
  setShowRecipientModal: (value: boolean) => void; // Correctly define function type
}

interface returnAmountData {
  final_rate: number;
  from: string;
  inductive_fx_rate: number;
  return_amount: number;
  rf_rate: number;
  to: string;
  trade_fee: number;
}

interface DataValue {
  type: string;
  message: string;
  error: string;
  data: {
    action: string;
    button: string;
    flag: string;
    message: string;
    next_action: string;
    order_id: string;
    stepper: string;
    time_line_state: string;
  };
}

const SearchExchModal = ({
  isVisible,
  onClose,
  setShowRecipientModal,
  rate,
  amount,
  available_liquidity,
  min_liquidity,
  max_liquidity,
  ListedOn,
  timeLimit,
  payin_option,
  payout_option,
  terms,
  showExchModal,
  payInCurrency,
  payOutCurrency,
  listingId,
}: SearchExchModalProps) => {
  const router = useRouter();

  const [enteredAmount, setEnteredAmount] = useState(amount ? amount : "");
  const [returnAmount, setReturnedAmount] = useState<returnAmountData>();
  const [minLiquidityMessage, setMinLiquidityMessage] = useState<string>("");
  const [orderId, setOrderId] = useState("");
  const [showOnce, setShowOnce] = useState(false);



  // useEffect(() => {
  //   const dataValue = lastJsonMessage as DataValue | null;
  //   console.log("dataValue", dataValue);
  //   if (dataValue?.error && listingId && showExchModal) {
  //     showToastError(dataValue?.error);
  //   }

  //   if (dataValue?.data?.message === "Trade request accepted") {
  //     setOrderId(dataValue?.data?.order_id);
  //     router.push(`/trade/${dataValue?.data?.order_id}?type=user`);
  //   } else if (dataValue?.data?.order_id && showExchModal) {
  //     if (dataValue?.error === "Trade request already sent") {
  //       setOrderId(dataValue?.data?.order_id);
  //       showToastError("Trade request already sent");
  //     } else if (dataValue?.data?.order_id && showExchModal) {
  //       setOrderId(dataValue?.data?.order_id);
  //       if (!showOnce) {
  //         showToastSuccess(dataValue?.data?.message);
  //         setShowOnce(true);
  //         setTimeout(() => {
  //           router.push(`/trade/${dataValue?.data?.order_id}?type=user`);
  //         }, 1000);
  //       }
  //     }
  //   }
  // }, [lastJsonMessage, listingId, showExchModal, showOnce]);

 
  const handleTradeRequest = async () => {
    // Check if WebSocket is connected

    try {
      const payload = {
        listing_id: Number(listingId),
        trade_amount: Number(enteredAmount),
      };

      const response = await customFetchWithToken.post("/trade/send-request/", {
        payload,
      });

      if (response.status === 200) {
        showToastSuccess(response.data.message);
        setTimeout(() => {
          router.push(`/trade/${response.data.order_id}?type=user`);
        }, 1000);
      } else {
        showToastError("Failed to send trade request. Please try again.");
      }

    } catch (error) {
      console.error("Error sending trade request", error);
      showToastError("Failed to send trade request. Please try again.");
    }
  };

  const createTradeRequest = () => {
    if (payOutCurrency === "USDT") {
      try {
        handleTradeRequest();
        // showToastSuccess("Sending trade request");
      } catch (error) {
        console.log(error);
      }
    } else {
      setShowRecipientModal(true); // Use correct type
      onClose();
    }
  };

  // websocket

  const openRecipientModal = () => {
    setShowRecipientModal(true); // Use correct type
    onClose();
  };

  const handleAmountExchMessage = () => {
    if (Number(enteredAmount) < min_liquidity && enteredAmount.length > 0) {
      setMinLiquidityMessage("Amount is less than minimum liquidity");
    } else if (
      Number(enteredAmount) > max_liquidity &&
      enteredAmount.length > 0
    ) {
      setMinLiquidityMessage("Amount is more than maximum liquidity");
    } else {
      setMinLiquidityMessage("");
    }
  };

  const modalRateExch = async () => {
    if (Number(enteredAmount) < 1 && !showExchModal) {
      return null;
    }

    try {
      const res = await customFetchWithToken(
        `/current-currency-rate/?from_currency=${payInCurrency}&to_currency=${payOutCurrency}&amount=${Number(
          enteredAmount
        )}&listing_id=${listingId}`
      );
      setReturnedAmount(res.data.data);
    } catch (error) {
      console.log("error", error);
    }
  };

  // Debounced function
  const debouncedModalRateExch = useCallback(debounce(modalRateExch, 500), [
    enteredAmount,
    amount,
  ]);

  useEffect(() => {
    debouncedModalRateExch();
    handleAmountExchMessage();
    return debouncedModalRateExch.cancel; // Cleanup on unmount
  }, [debouncedModalRateExch]);

  return (
    <Modal visible={true} transparent={false} animationType="slide">
      <View className="h-screen bg-white w-full mt-10">
        <ScrollView>
          <View className=" mt-5  flex flex-row justify-between items-center px-4">
            <View className="relative">
              <Image
                className="w-14 h-14 object-fill"
                source={require("../assets/images/react-logo.png")}
              />
            </View>
            <View>
              <Text className="font-pbold py-1 text-lg">User 1</Text>
            </View>
            <TouchableOpacity onPress={onClose}>
              <View>
                <MaterialIcons name="cancel" size={40} color="black" />
              </View>
            </TouchableOpacity>
          </View>
          <View className="px-4 ">
            <Text className="font-pmedium py-1 text-lg">
              Seen 15 minutes ago
            </Text>
          </View>
          <View className="px-4 ">
            <Text className="font-pmedium py-1 text-lg">
              48 Orders 100% Completion 95.76%
            </Text>
          </View>
          <View>
            <View className="px-2 py-2 border-dashed border border-[#8a2be2] mx-2">
              <View>
                <Text className="font-pmedium  text-md">
                  Rate: {rate.toString()}
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Available : {available_liquidity}
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Minimum Liquidity : {min_liquidity}
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Maximum Liquidity : {max_liquidity}
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Listed on : {ListedOn.split("T")[0]}
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Payment time limit : {timeLimit} Minutes
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Seller payIn Method : {payin_option}
                </Text>
              </View>
              <View>
                <Text className="font-pmedium  text-md">
                  Seller payOut Method : {payout_option}
                </Text>
              </View>
            </View>
            {/* emdd */}
            <View>
              {/* i want to pay */}
              <View className="mx-4 mt-2">
                <Text className="font-pmedium  text-lg">I want to pay</Text>
                {minLiquidityMessage && enteredAmount.length > 0 ? (
                  <Text className="font-pmedium  text-lg text-red-600">
                    {minLiquidityMessage}
                  </Text>
                ) : (
                  ""
                )}
                <View className="relative">
                  <TextInput
                    className="border border-gray-300 rounded-lg px-4 py-4 w-full text-gray-800 mb-4 font-psemitBold"
                    placeholder="enter amount"
                    maxLength={10}
                    keyboardType="number-pad"
                    autoCapitalize="none"
                    value={enteredAmount ? enteredAmount : ""}
                    onChangeText={(amount) => setEnteredAmount(amount)}
                  />
                  <View className="absolute right-3 top-3">
                    <Text className="font-pmedium py-1 text-lg">
                      {payInCurrency}
                    </Text>
                  </View>
                </View>
              </View>
              {/* i want to pay */}
              {/* i will receive */}
              <View className="mx-4 mb-1">
                <Text className="font-pmedium py-1 text-lg">
                  I will receive
                </Text>
                <View className="relative">
                  <TextInput
                    className="border border-gray-300 rounded-lg px-4 py-4 w-full text-gray-800 mb-4 font-psemitBold"
                    placeholder="xxxxxxxxxxx"
                    keyboardType="number-pad"
                    autoCapitalize="none"
                    value={
                      returnAmount?.return_amount
                        ? String(returnAmount.return_amount.toFixed(2))
                        : ""
                    }
                    //   onChangeText={setSecOtp}
                  />
                  <View className="absolute right-3 top-3">
                    <Text className="font-pmedium py-1 text-lg">
                      {payOutCurrency}
                    </Text>
                  </View>
                </View>
              </View>
              {/* i will receive */}
              <TouchableOpacity onPress={createTradeRequest}>
                <View className="h-12 bg-[#50CD89] mx-4 rounded-md  flex justify-center items-center">
                  <Text className="text-center font-pbold text-lg text-green-800">
                    Buy INR
                  </Text>
                </View>
              </TouchableOpacity>
              <View>
                <Text className="font-pmedium py-1 text-lg mx-4">
                  Terms and Conditions
                </Text>
              </View>
              <View>
                <Text className="font-pmedium py-1 text-lg mx-4 mb-8">
                  {terms}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default SearchExchModal;
