import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { styled } from "nativewind";
import { Ionicons } from "@expo/vector-icons";
import TradeCard from "../components/searchResultsCards";
import Layout from "@/components/Layout";
import customFetchWithToken from "./utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "bold",
        marginLeft: 20,
      }}
      text2Style={{
        fontSize: 29,
      }}
    />
  ),
  // You can add similar customizations for 'error' and 'info' types if needed
};
const showToastSuccess = (message: string) => {
  Toast.show({
    type: "success", // can also be 'error' or 'info'
    text1: message,
    position: "top", // or 'bottom'
    visibilityTime: 4000, // duration in milliseconds
  });
};
const showToastError = (message: string) => {
  Toast.show({
    type: "error", // can also be 'error' or 'info'
    text1: message,
    // text2: "This is a toast message 👋",
    position: "top", // or 'bottom'
    visibilityTime: 4000, // duration in milliseconds
  });
};

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTextInput = styled(TextInput);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface currencyAccepted {
  currency_code: string;
  currency_name: string;
  id: number;
}

interface PaymentOption {
  id: number;
  payment_method: string;
}
interface User {
  id: number;
  email: string;
  firstname: string;
  lastname: string;
  username: string;
  img_logo: string;
  last_login: string; // ISO date string
}
interface TradeItem {
  id: number;
  user: {
    username: string;
  };
  lsiting_id: number;
  final_trade_fee: string;
  available_liquidity: number;
  currency_accepted: {
    currency_code: string;
  };
  currency_payout: {
    currency_code: string;
  };
  payin_option: {
    payment_method: string;
  };
  payout_option: {
    payment_method: string;
  };
  min_liquidity: number;
  max_liquidity: number;
  time_limit: number;
  created_date: string;
  terms_and_conditions: string;
}
interface TradeOffer {
  user: User;
  currency_accepted: currencyAccepted;
  currency_payout: currencyAccepted;
  id: number;
  lsiting_id: number;
  trades: number;
  successRate: string;
  rateSpread: string;
  final_trade_fee: string;
  available_liquidity: number;
  name: string;
  currencyAccepted: currencyAccepted;
  currencyPayout: currencyAccepted;
  payin_option: PaymentOption;
  payout_option: PaymentOption;
  min_liquidity: number;
  max_liquidity: number;
  time_limit: number;
  created_date: string;
  terms_and_conditions: string;
}
interface Currency {
  currency_code: string;
  currency_name: string;
  currency_type: "fiat" | "crypto";
  id: number;
  priority: number;
}
interface PaymentMethodTypes {
  country_name: string;
  id: Number;
  payment_method: string;
}

export default function SearchFilter() {
  const BaseURL = process.env.EXPO_PUBLIC_Base_URL;
  const [isExpanded, setIsExpanded] = useState(false);
  const [amount, setAmount] = useState("");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [loadCurrencyPayout, setLoadCurrencyPayout] = useState<Currency[]>([]);
  const [loadPaymentAccepted, setLoadPaymentAccepted] = useState<
    PaymentMethodTypes[]
  >([]);
  const [loadPaymentPayout, setLoadPaymentPayout] = useState<
    PaymentMethodTypes[]
  >([]);
  const [loadCurrencyAccepted, setLoadCurrencyAccepted] = useState<Currency[]>(
    []
  );
  const [paymentMethodAccepted, setPaymentMethodAccepted] = useState("");
  const [paymentMethodPayout, setPaymentMethodPayout] = useState("");
  const [currencyAccepted, setCurrencyAccepted] = useState("");
  const [currencyPayout, setCurrencyPayout] = useState("");
  const [toUsdt, setToUsdt] = useState(true);
  const [fromUsdt, setFromUsdt] = useState(false);
  const [otherData, setOtherData] = useState([]);
  const [otherData1, setOtherData1] = useState([]);
  const [bestRate, setBestRate] = useState("");
  const [data, setData] = useState<TradeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [noOfRecords, setNoOfRecords] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const ITEMS_PER_PAGE = 10;

  const fetchAcceptedCurrency = async () => {
    try {
      const res = await customFetchWithToken(
        "/currency-list/?currency_from=true"
      );

      setLoadCurrencyAccepted(res.data.data);
      setCurrencyAccepted(res.data.data[0].currency_code);
    } catch (error: any) {
      console.log(error.response.data);
    }
  };
  const fetchpayoutCurrency = async () => {
    try {
      const res = await customFetchWithToken(
        "/currency-list/?currency_to=true"
      );

      setLoadCurrencyPayout(res.data.data);
      setCurrencyPayout(res.data.data[0].currency_code);
    } catch (error: any) {
      console.log(error.response.data);
    }
  };

  const fetchPaymentMethodsAccepted = async () => {
    try {
      const resCurrency = await customFetchWithToken(
        `/payment-list/?currency=${currencyAccepted}`
      );

      setLoadPaymentAccepted(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };
  const fetchPaymentMethodsPayout = async () => {
    try {
      const resCurrency = await customFetchWithToken(
        `/payment-list/?currency=${currencyPayout}`
      );

      setLoadPaymentPayout(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const handleUsdtToCurrency = async () => {
    setToUsdt(false);
    setFromUsdt(true);
    // setData(otherData);
    setLoading(true);
    try {
      const response = await customFetchWithToken(
        `/get-listings-data/?currency_accepted=USDT&currency_payout=${currencyPayout}&payout_option=${paymentMethodPayout}&available_liquidity=${amount}&rate_sort=${sortDirection}&page=${page}&flag=crypto_to_fiat`
      );

      const PassData = response?.data?.results?.data;
      // const otherData1 = resData.other_listing;
      if (!PassData || PassData.length < 1) {
        showToastError("No data available for this Search");
      }
      setData(PassData);
      setOtherData1(PassData);
      // setOtherData(otherData1);
      setNoOfRecords(PassData?.count || 0);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSearchResults = async (pageNum = 1) => {
    if (isFetching) return;

    setIsFetching(true);
    setToUsdt(true);
    setFromUsdt(false);

    if (pageNum === 1) {
      setLoading(true);
    } else {
      setIsLoadingMore(true);
    }

    try {
      const response = await customFetchWithToken(
        `/get-listings-data/?currency_accepted=${currencyAccepted}&currency_payout=${currencyPayout}&payin_option=${paymentMethodAccepted}&payout_option=${paymentMethodPayout}&available_liquidity=${amount}&rate_sort=${sortDirection}&page=${pageNum}`
      );
      const resData = response.data;

      setBestRate(resData.results.best_rate?.toFixed(2));
      const PassData = resData.results.data;

      if (PassData.length < 1 && pageNum === 1) {
        showToastError("No data available for this Search");
      }

      // Update data based on page number
      if (pageNum === 1) {
        setData(PassData);
      } else {
        setData((prevData) => [...prevData, ...PassData]);
      }

      setOtherData1(PassData);
      setOtherData(resData.other_listing);
      setNoOfRecords(resData.count);

      // Set hasMore based on whether there's a next page
      setHasMore(resData.next !== null);

      // Update page number
      setPage(pageNum);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
      setIsFetching(false);
    }
  };

  const handleLoadMore = () => {
    if (!isFetching && hasMore) {
      const nextPage = page + 1;
      fetchSearchResults(nextPage);
    }
  };

  const handleSearch = () => {
    setPage(1); // Reset page number
    setHasMore(true); // Reset hasMore
    fetchSearchResults(1); // Fetch first page
  };

  const toggleSort = () => {
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const handleBuyPress = (elId: number) => {
    console.log(`Buy pressed for el ${elId}`);
  };
  useEffect(() => {
    fetchAcceptedCurrency();
    fetchpayoutCurrency();
  }, []);
  useEffect(() => {
    if (currencyAccepted && currencyPayout) {
      fetchSearchResults();
    }
  }, [sortDirection, currencyAccepted, currencyPayout]);

  useEffect(() => {
    if (currencyAccepted) {
      fetchPaymentMethodsAccepted();
    }
  }, [currencyAccepted]);
  useEffect(() => {
    if (currencyPayout) {
      fetchPaymentMethodsPayout();
    }
  }, [currencyPayout]);

  const renderTradeCard = useCallback(
    ({ item }: { item: TradeItem }) => (
      <TradeCard
        key={item.id}
        name={item?.user?.username}
        amount={amount}
        listingId={item.lsiting_id}
        trades={49}
        successRate="100%"
        rateSpread={"95.76%"}
        exchangeRate={item.final_trade_fee}
        quantity={item.available_liquidity}
        payInCurrency={item.currency_accepted?.currency_code}
        payOutCurrency={item.currency_payout?.currency_code}
        payInOption={item.payin_option?.payment_method}
        payOutOption={item.payout_option?.payment_method}
        min_liquidity={item.min_liquidity}
        max_liquidity={item.max_liquidity}
        time_limit={item.time_limit}
        created_date={item.created_date}
        terms={item.terms_and_conditions}
        onBuyPress={() => handleBuyPress(item.id)}
      />
    ),
    [amount, handleBuyPress]
  );

  const keyExtractor = useCallback(
    (item: TradeItem) =>
      item.id ? item.id.toString() : Math.random().toString(),
    []
  );

  const ListFooterComponent = useCallback(
    () =>
      isLoadingMore ? (
        <StyledView className="py-4">
          <ActivityIndicator size="small" color="#0000ff" />
        </StyledView>
      ) : null,
    [isLoadingMore]
  );

  const getItemLayout = useCallback(
    (data: ArrayLike<TradeItem> | null | undefined, index: number) => ({
      length: 200,
      offset: 200 * index,
      index,
    }),
    []
  );

  return (
    <Layout>
      <FlatList
        className="flex-1 bg-white p-4"
        data={[{ key: "content" }]}
        renderItem={() => (
          <>
            <StyledView className="space-y-4">
              {/* Currency Selection */}
              <StyledView className="flex-row justify-between items-center">
                <StyledView className="flex-1 mr-2">
                  <StyledText className="mb-1 text-sm">From</StyledText>
                  <StyledView className="bg-gray-50 rounded-md">
                    <Picker
                      selectedValue={currencyAccepted}
                      onValueChange={(itemValue) =>
                        setCurrencyAccepted(itemValue)
                      }
                      className="bg-white font-pmedium"
                    >
                      {loadCurrencyAccepted?.map((el, index) => (
                        <Picker.Item
                          key={index}
                          label={el.currency_code}
                          value={el.currency_code}
                        />
                      ))}
                    </Picker>
                  </StyledView>
                </StyledView>

                <StyledView className="flex-1 ml-2">
                  <StyledText className="mb-1 text-sm">To</StyledText>
                  <StyledView className="bg-gray-50 rounded-md">
                    <Picker
                      selectedValue={currencyPayout}
                      onValueChange={(itemValue) =>
                        setCurrencyPayout(itemValue)
                      }
                      className="bg-white"
                    >
                      {loadCurrencyPayout?.map((el, index) => (
                        <Picker.Item
                          key={index}
                          label={el.currency_code}
                          value={el.currency_code}
                        />
                      ))}
                    </Picker>
                  </StyledView>
                </StyledView>

                <TouchableOpacity
                  onPress={() => setIsExpanded(!isExpanded)}
                  className="ml-2 p-2"
                >
                  <Ionicons name="filter" size={24} color="#000" />
                </TouchableOpacity>
              </StyledView>

              {/* Collapsible Section */}
              {isExpanded && (
                <>
                  {/* Payment Methods */}
                  <StyledView className="flex-row justify-between">
                    <StyledView className="flex-1 mr-2">
                      <StyledText className="mb-1 text-sm">
                        Payment From
                      </StyledText>
                      <StyledView className="bg-gray-50 rounded-md">
                        <Picker
                          selectedValue={paymentMethodAccepted}
                          onValueChange={(itemValue) =>
                            setPaymentMethodAccepted(itemValue)
                          }
                          className="bg-white"
                        >
                          <Picker.Item label="Select Payment Method" value="" />
                          {loadPaymentAccepted?.map((el, index) => (
                            <Picker.Item
                              key={index}
                              label={el.payment_method}
                              value={el.payment_method}
                            />
                          ))}
                        </Picker>
                      </StyledView>
                    </StyledView>

                    <StyledView className="flex-1 ml-2">
                      <StyledText className="mb-1 text-sm">
                        Payment To
                      </StyledText>
                      <StyledView className="bg-gray-50 rounded-md">
                        <Picker
                          selectedValue={paymentMethodPayout}
                          onValueChange={(itemValue) =>
                            setPaymentMethodPayout(itemValue)
                          }
                          className="bg-white"
                        >
                          <Picker.Item
                            label="Select a Payout Currency First"
                            value=""
                          />
                          {loadPaymentPayout?.map((el, index) => (
                            <Picker.Item
                              key={index}
                              label={el.payment_method}
                              value={el.payment_method}
                            />
                          ))}
                        </Picker>
                      </StyledView>
                    </StyledView>
                  </StyledView>

                  {/* Amount Input */}
                  <StyledView>
                    <StyledText className="mb-1 text-sm">Amount</StyledText>
                    <StyledTextInput
                      className="bg-gray-50 p-3 rounded-md"
                      placeholder="enter Amount"
                      value={amount}
                      maxLength={10}
                      onChangeText={setAmount}
                      keyboardType="numeric"
                    />
                  </StyledView>
                </>
              )}

              {/* Search Button */}
              <StyledTouchableOpacity
                className="bg-blue-600 p-4 rounded-md"
                onPress={handleSearch}
              >
                <StyledText className="text-white text-center font-pmedium">
                  Search
                </StyledText>
              </StyledTouchableOpacity>
              {/* Swap Currencires */}
              {bestRate && bestRate.length ? (
                <StyledView className="w-full flex flex-row justify-center items-center">
                  <StyledView
                    style={
                      toUsdt
                        ? { backgroundColor: "#50CD89" }
                        : { backgroundColor: "#b4b2b2" }
                    }
                    className="w-2/4   font-pmedium text-md"
                  >
                    <TouchableOpacity onPress={() => fetchSearchResults(1)}>
                      <StyledText className="text-lg py-3 text-center">
                        {currencyAccepted}- USDT
                      </StyledText>
                    </TouchableOpacity>
                  </StyledView>
                  <StyledView
                    style={
                      fromUsdt
                        ? { backgroundColor: "#50CD89" }
                        : { backgroundColor: "#b4b2b2" }
                    }
                    className="w-2/4    font-pmedium text-md"
                  >
                    <TouchableOpacity onPress={handleUsdtToCurrency}>
                      <StyledText className="text-lg py-3 text-center">
                        USDT-{currencyPayout}
                      </StyledText>
                    </TouchableOpacity>
                  </StyledView>
                </StyledView>
              ) : (
                ""
              )}
              {/* Swap Currencires */}
              {/* Filter by rate */}
              <StyledView className="flex-row justify-end items-center">
                <TouchableOpacity
                  onPress={toggleSort}
                  className="flex-row items-center"
                >
                  <StyledText className="mr-2">Filter by rate</StyledText>
                  <Ionicons
                    name={
                      sortDirection === "asc" ? "chevron-up" : "chevron-down"
                    }
                    size={20}
                    color="#000"
                  />
                </TouchableOpacity>
              </StyledView>

              {/* Trade els */}
              <FlatList
                data={data}
                renderItem={renderTradeCard}
                keyExtractor={keyExtractor}
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.5}
                ListFooterComponent={ListFooterComponent}
                initialNumToRender={5}
                maxToRenderPerBatch={3}
                windowSize={3}
                removeClippedSubviews={true}
                getItemLayout={getItemLayout}
                updateCellsBatchingPeriod={50}
                maintainVisibleContentPosition={{
                  minIndexForVisible: 0,
                  autoscrollToTopThreshold: 10,
                }}
              />
            </StyledView>
            <Toast config={toastConfig} />
          </>
        )}
        keyExtractor={(item) => item.key}
      />
    </Layout>
  );
}
