import React, { useState, useEffect, useRef } from "react";
import * as SecureStore from "expo-secure-store";
import { View, Text, TouchableOpacity, FlatList } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Picker } from "@react-native-picker/picker";
import Layout from "../components/Layout";
import AccountsPayAddModal from "../components/AccountsAddPayModal";
import AccountsAddRecipientModal from "../components/AccountsAddRecipientModal";
import customFetchWithToken from "./utils/axiosInterceptor";
import { toastConfig, showToastSuccess, showToastError } from "../hooks/toast";
import AccountCard from "@/components/AccountsUserCard";
import AccountsRecipientCard from "../components/AccountsRecipientCard";

import ConfirmationModal from "../components/ConfirmationModal";

interface PaymentDetail {
  key: string; // The label for the detail, e.g., "GB account / IBAN"
  value: string; // The value for the detail, e.g., "************"
}

interface PaymentData {
  id: number; // Unique identifier for the payment method
  payment_method: string; // Description of the payment method, e.g., "Bank Transfer(UK)"
  data: PaymentDetail[]; // Array of key-value pairs with payment details
}

interface PaymentDetail {
  key: string;
  value: string;
}

interface RecipientData {
  data: PaymentDetail[];
  recipient_id__country__country_name: string; // Country name, e.g., "India"
  recipient_id__created_date: string; // Creation date in ISO format
  recipient_id__currency_accepted_id__currency_name: string | null;
  recipient_id__currency_payout_id__currency_name: string | null;
  recipient_id__dob: string | null; // Date of birth in ISO format, nullable
  recipient_id__email: string; // Email address
  recipient_id__firstname: string | null; // First name, nullable
  recipient_id__id: number; // Unique identifier for the recipient
  recipient_id__is_active: boolean; // Indicates if the recipient is active
  recipient_id__is_deleted: boolean; // Indicates if the recipient is deleted
  recipient_id__lastname: string | null; // Last name, nullable
  recipient_id__payin_option_id__payment_method: string | null; // Payment-in method, nullable
  recipient_id__payout_option_id__payment_method: string | null; // Payout method, nullable
  recipient_id__surname: string | null; // Surname, nullable
  recipient_id__type: string; // Recipient type, e.g., "Person" or "Business"
  recipient_id__ubo_shareholder_date_of_incorporation: string | null;
  recipient_id__ubo_shareholder_name: string | null; // Shareholder name, nullable
  recipient_id__updated_date: string; // Updated date in ISO format
  recipient_id__user_id: number; // User ID associated with the recipient
}

export default function Accounts() {
  const userIdRef = useRef<string | null>(null);
  const [showAddAccModal, setShowAddAccModal] = useState(false);
  const [showAddRecModal, setShowAddRecModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userPayDataAll, setUserPayDataAll] = useState<any>([]);
  const [userPayData, setUserPayData] = useState<any>([]);
  const [recipientPayData, setRecipientPayData] = useState<RecipientData[]>([]);
  const [savedDisplayPayment, setSavedDisplayPayment] = useState<boolean>(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedTradeId, setSelectedTradeId] = useState<number | null>(null);
  const [isConfirmationModalVisible, setIsConfirmationModalVisible] =
    useState(false);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  console.log("recipientPayData", recipientPayData);

  // New state for filters
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState<
    Array<{ currency_code: string; currency_name: string }>
  >([]);
  const [loadPaymentMethods, setLoadPaymentMethods] = useState<
    Array<{ payment_method: string }>
  >([]);
  const [selectedCurrency, setSelectedCurrency] = useState("");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("");

  const userPaymentData = async () => {
    setLoading(true);
    setSavedDisplayPayment(true);
    try {
      const res = await customFetchWithToken.get("/user-payment-fields-data/");
      if (res?.data?.data) {
        setUserPayDataAll(res.data.data);
        setUserPayData(res.data.data);
      } else {
        console.error("No data received from API.");
        showToastError("No payment data received.");
      }
    } catch (error: any) {
      console.error("Error fetching user payment data:", error);
      const errorMessage =
        error?.response?.data?.message || "Something went wrong.";
      showToastError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrencyList = async () => {
    try {
      const res = await customFetchWithToken.get("/currency-list/");
      setLoadCurrencyFrom(res.data.data);
    } catch (error: any) {
      console.error("Error fetching currency list:", error);
      showToastError("Failed to load currencies");
    }
  };

  const fetchPaymentMethods = async (currency: string) => {
    try {
      const res = await customFetchWithToken.get(
        `/payment-list/?currency=${currency}`
      );
      setLoadPaymentMethods(res.data.data);
    } catch (error: any) {
      console.error("Error fetching payment methods:", error);
      showToastError("Failed to load payment methods");
    }
  };

  const handleCurrencyChange = (value: string) => {
    setSelectedCurrency(value);
    setSelectedPaymentMethod(""); // Reset payment method when currency changes
    if (value) {
      fetchPaymentMethods(value);
    } else {
      setLoadPaymentMethods([]);
    }
  };

  const handlePaymentMethodChange = (value: string) => {
    setSelectedPaymentMethod(value);
  };

  const applyFilters = () => {
    if (!selectedPaymentMethod) {
      setUserPayData(userPayDataAll);
      return;
    }

    const filteredData = userPayDataAll.filter(
      (item: any) => item.payment_method === selectedPaymentMethod
    );
    setUserPayData(filteredData);
  };

  const userRecipientPaymentData = async () => {
    setLoading(true);
    setSavedDisplayPayment(false);
    try {
      const res = await customFetchWithToken.get("/get-all-recipient/");
      console.log("res23", res.data.data);

      const returnArray = res.data.data;

      const filteredDataByUser = returnArray.filter(
        (data: RecipientData) =>
          data.recipient_id__user_id !== Number(userIdRef)
      );

      setRecipientPayData(filteredDataByUser);
    } catch (error: any) {
      console.error(error);
      console.log("err", error);
      showToastError(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    userPaymentData();
    fetchCurrencyList();
  }, []);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userIdNumber = await SecureStore.getItemAsync("userID");

        if (userIdNumber) {
          userIdRef.current = userIdNumber;
        }
      } catch (error) {
        console.error("Failed to load user data:", error);
      }
    };

    loadUserData();
  }, []);

  const handleOpenReviewModal = () => {
    setIsConfirmationModalVisible(!isConfirmationModalVisible);
  };

  const handleSubmitReview = (rating: number, comment: string) => {
    console.log(
      "Submitting review:",
      { rating, comment },
      "for trade:",
      selectedTradeId
    );
    setIsModalVisible(false);
    setSelectedTradeId(null);
  };

  const renderHeader = () => (
    <View className="p-4">
      <View className="flex-row justify-between items-center mb-6">
        <Text className="text-2xl font-bold">Accounts</Text>
      </View>

      {/* Action Buttons */}
      <View className="mb-8">
        <View className="flex-row items-center justify-between mb-6">
          <View>
            {/* <Text className="text-2xl font-bold text-gray-900">
              Quick Actions
            </Text> */}
            <Text className="text-md text-gray-500 mt-1">
              Manage your payment accounts
            </Text>
          </View>
          {/* <View className="bg-blue-50 p-2 rounded-full">
            <Ionicons name="settings-outline" size={20} color="#3B82F6" />
          </View> */}
        </View>

        <View className="space-y-4">
          {/* Add New Accounts Section */}
          <View className="bg-blue-500 p-5 rounded-2xl shadow-lg">
            <Text className="text-white font-bold text-lg mb-4">
              Add New Accounts
            </Text>
            <View className="flex-row space-x-3">
              <TouchableOpacity
                className="flex-1 bg-blue-400 p-4 rounded-xl border border-blue-300"
                onPress={() => setShowAddAccModal(true)}
                activeOpacity={0.8}
              >
                <View className="items-center">
                  <Ionicons name="card-outline" size={24} color="white" />
                  <Text className="text-white font-semibold text-sm mt-2 text-center">
                    Payin Account
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-1 bg-blue-400 p-4 rounded-xl border border-blue-300"
                onPress={() => setShowAddRecModal(true)}
                activeOpacity={0.8}
              >
                <View className="items-center">
                  <Ionicons name="person-add-outline" size={24} color="white" />
                  <Text className="text-white font-semibold text-sm mt-2 text-center">
                    Recipient
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* View Saved Accounts Section */}
          <View className="bg-white p-5 rounded-2xl shadow-sm border border-gray-100">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-gray-900 font-bold text-lg">
                Saved Accounts
              </Text>
              <View className="bg-gray-100 px-3 py-1 rounded-full">
                <Text className="text-gray-600 text-xs font-medium">
                  Quick Access
                </Text>
              </View>
            </View>

            <View className="space-y-3">
              <TouchableOpacity
                className="flex-row items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200"
                onPress={userPaymentData}
                activeOpacity={0.7}
              >
                <View className="flex-row items-center">
                  <View className="bg-green-100 p-2 rounded-lg mr-3">
                    <Ionicons name="wallet-outline" size={20} color="#059669" />
                  </View>
                  <View>
                    <Text className="text-gray-900 font-semibold">
                      My Payment Methods
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      View and manage payin accounts
                    </Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-row items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200"
                onPress={userRecipientPaymentData}
                activeOpacity={0.7}
              >
                <View className="flex-row items-center">
                  <View className="bg-purple-100 p-2 rounded-lg mr-3">
                    <Ionicons name="people-outline" size={20} color="#7C3AED" />
                  </View>
                  <View>
                    <Text className="text-gray-900 font-semibold">
                      My Recipients
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      Manage saved beneficiaries
                    </Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Filter Section */}
      <View className="mb-6">
        {/* Filter Toggle Header */}
        <TouchableOpacity
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 flex-row justify-between items-center"
          onPress={() => setIsFilterExpanded(!isFilterExpanded)}
          activeOpacity={0.7}
        >
          <View className="flex-row items-center">
            <View className="bg-blue-50 p-2 rounded-full mr-3">
              <Ionicons name="filter" size={20} color="#3B82F6" />
            </View>
            <View>
              <Text className="text-lg font-semibold text-gray-800">
                Filter Accounts
              </Text>
              <Text className="text-sm text-gray-500">
                {selectedCurrency && selectedPaymentMethod
                  ? `${selectedCurrency} • ${selectedPaymentMethod}`
                  : "Tap to filter your saved accounts"}
              </Text>
            </View>
          </View>
          <View className="bg-gray-50 p-2 rounded-full">
            <Ionicons
              name={isFilterExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color="#6B7280"
            />
          </View>
        </TouchableOpacity>

        {/* Collapsible Filter Content */}
        {isFilterExpanded && (
          <View className="bg-white mt-2 p-4 rounded-lg shadow-sm border border-gray-100">
            <View className="space-y-4">
              {/* Currency Selection */}
              <View>
                <Text className="text-sm font-medium text-gray-700 mb-2">
                  Select Currency
                </Text>
                <View className="border border-gray-200 rounded-lg bg-gray-50">
                  <Picker
                    selectedValue={selectedCurrency}
                    onValueChange={handleCurrencyChange}
                    style={{ height: 50 }}
                  >
                    <Picker.Item
                      label="Choose a currency..."
                      value=""
                      color="#9CA3AF"
                    />
                    {loadCurrencyFrom.map((currency, index) => (
                      <Picker.Item
                        key={index}
                        label={`${currency.currency_code} - ${currency.currency_name}`}
                        value={currency.currency_code}
                        color="#374151"
                      />
                    ))}
                  </Picker>
                </View>
              </View>

              {/* Payment Method Selection */}
              <View>
                <Text className="text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </Text>
                <View
                  className={`border rounded-lg ${
                    selectedCurrency
                      ? "border-gray-200 bg-gray-50"
                      : "border-gray-100 bg-gray-100"
                  }`}
                >
                  <Picker
                    selectedValue={selectedPaymentMethod}
                    onValueChange={handlePaymentMethodChange}
                    enabled={!!selectedCurrency}
                    style={{
                      height: 50,
                      opacity: selectedCurrency ? 1 : 0.5,
                    }}
                  >
                    <Picker.Item
                      label={
                        selectedCurrency
                          ? "Choose payment method..."
                          : "Select currency first"
                      }
                      value=""
                      color="#9CA3AF"
                    />
                    {loadPaymentMethods.map((method, index) => (
                      <Picker.Item
                        key={index}
                        label={method.payment_method}
                        value={method.payment_method}
                        color="#374151"
                      />
                    ))}
                  </Picker>
                </View>
              </View>

              {/* Action Buttons */}
              <View className="flex-row space-x-3 pt-2">
                <TouchableOpacity
                  className={`flex-1 py-3 rounded-lg flex-row justify-center items-center ${
                    selectedCurrency && selectedPaymentMethod
                      ? "bg-blue-500 shadow-sm"
                      : "bg-gray-200"
                  }`}
                  onPress={applyFilters}
                  disabled={!selectedCurrency || !selectedPaymentMethod}
                  activeOpacity={0.8}
                >
                  <Ionicons
                    name="checkmark-circle"
                    size={18}
                    color={
                      selectedCurrency && selectedPaymentMethod
                        ? "white"
                        : "#9CA3AF"
                    }
                    style={{ marginRight: 6 }}
                  />
                  <Text
                    className={`font-semibold ${
                      selectedCurrency && selectedPaymentMethod
                        ? "text-white"
                        : "text-gray-400"
                    }`}
                  >
                    Apply Filter
                  </Text>
                </TouchableOpacity>

                {(selectedCurrency || selectedPaymentMethod) && (
                  <TouchableOpacity
                    className="px-4 py-3 rounded-lg border border-gray-300 flex-row justify-center items-center"
                    onPress={() => {
                      setSelectedCurrency("");
                      setSelectedPaymentMethod("");
                      setUserPayData(userPayDataAll);
                    }}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name="refresh"
                      size={18}
                      color="#6B7280"
                      style={{ marginRight: 6 }}
                    />
                    <Text className="text-gray-600 font-medium">Reset</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );

  const renderContent = () => {
    if (showAddAccModal) {
      return (
        <AccountsPayAddModal
          isVisible={showAddAccModal}
          onClose={() => setShowAddAccModal(false)}
        />
      );
    }

    if (showAddRecModal) {
      return (
        <AccountsAddRecipientModal
          isVisible={showAddRecModal}
          onClose={() => setShowAddRecModal(false)}
        />
      );
    }

    if (savedDisplayPayment) {
      return (
        <View className="px-4">
          <Text className="text-xl text-center font-semibold mb-4">
            Saved Payin Accounts
          </Text>
          {!loading ? (
            userPayData.map((account: any) => (
              <AccountCard
                key={account.id}
                id={account.id}
                item={account.data}
                method={account.payment_method}
                onUpdate={userPaymentData}
                currency={account.country_code}
                country={account.country_name}
              />
            ))
          ) : (
            <Text className="text-center text-2xl font-pmedium">Loading</Text>
          )}
        </View>
      );
    }

    return (
      <View className="px-4">
        <Text className="text-xl text-center font-semibold mb-4">
          Saved Recipient Accounts
        </Text>
        {!loading ? (
          recipientPayData.map((account) => (
            <AccountsRecipientCard
              key={account.recipient_id__id}
              userRecipientPaymentData={account.data}
              PayMethodName={
                account.recipient_id__payout_option_id__payment_method
              }
              id={account.recipient_id__id}
              countryName={account.recipient_id__country__country_name}
              email={account.recipient_id__email}
              firstName={account.recipient_id__firstname}
              lastName={account.recipient_id__lastname}
              type={account.recipient_id__type}
              ubo_shareholder_date_of_incorporation={
                account.recipient_id__ubo_shareholder_date_of_incorporation
              }
              ubo_shareholder_name={account.recipient_id__ubo_shareholder_name}
              dob={account.recipient_id__dob}
              currency_payout={
                account.recipient_id__currency_payout_id__currency_name
              }
              created_date={account.recipient_id__created_date}
              onDelete={userRecipientPaymentData}
            />
          ))
        ) : (
          <Text className="text-center text-2xl font-pmedium">Loading</Text>
        )}
      </View>
    );
  };

  return (
    <Layout>
      <FlatList
        className="flex-1 bg-gray-50"
        ListHeaderComponent={renderHeader}
        data={[{ key: "content" }]}
        renderItem={() => renderContent()}
        keyExtractor={(item) => item.key}
      />
      {isConfirmationModalVisible && (
        <ConfirmationModal
          isVisible={isConfirmationModalVisible}
          onClose={() => setIsConfirmationModalVisible(false)}
        />
      )}
      {/* {selectedTradeId !== null && (
        <TradeReviewModal
          isVisible={isModalVisible}
          onClose={() => {
            setIsModalVisible(false);
            setSelectedTradeId(null);
          }}
          onSubmit={handleSubmitReview}
          tradeOrderId={String(selectedTradeId)}
        />
      )} */}
    </Layout>
  );
}
