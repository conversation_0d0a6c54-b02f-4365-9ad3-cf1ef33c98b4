import { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Pressable,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { router } from "expo-router";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import Toast from "react-native-toast-message";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";

interface externalWallet {
  wallet_address: string;
  network: string;
}

interface RequestWithdrawModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const RequestWithdrawModal = ({
  isVisible,
  onClose,
}: RequestWithdrawModalProps) => {
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [walletAddress, setWalletAddress] = useState("");
  const [amount, setAmount] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [savedExternalWalletsArray, setSavedExternalWalletsArray] = useState<
    externalWallet[]
  >([]);
  const [selectedExternalWallet, setSelectedExternalWallet] = useState("");

  const getExternalWallets = async () => {
    try {
      const res = await customFetchWithToken.get("/get-external-wallet/");
      setSavedExternalWalletsArray(res.data.data);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCreateWithdrawlReq = async () => {
    try {
      const res = await customFetchWithToken.post("/create-withdraw-request/", {
        amount: Number(amount),
        address: selectedExternalWallet,
      });

      // Close the modal before showing the toast
      onClose();
      showToastSuccess(res.data.message);
    } catch (error: any) {
      showToastError(error.response.data.message);
    }
  };

  useEffect(() => {
    getExternalWallets();
  }, []);

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-center items-center bg-black/50">
        <View className="bg-white rounded-lg p-6 w-11/12 max-h-[90%]">
          <View className="overflow-y-scroll">
            <Text className="text-lg font-bold text-center mb-4">
              Request Withdraw
            </Text>

            {/* Select Network */}
            <View className="mb-4 ">
              <Text className="text-gray-700 mb-2">Select Network</Text>
              <View className="border border-gray-300 rounded-lg">
                <Picker
                  selectedValue={selectedNetwork}
                  onValueChange={(itemValue) => setSelectedNetwork(itemValue)}
                  style={{ height: 50, width: "100%" }}
                >
                  <Picker.Item label="Select Network" value="" />
                  <Picker.Item label="ERC/MATIC/BNB" value="ERC" />
                  <Picker.Item label="TRC" value="TRC" />
                </Picker>
              </View>
            </View>

            {/* Wallet Address */}
            <View className="mb-4 ">
              <Text className="text-gray-700 mb-2">Select External Wallet</Text>
              <View className="border border-gray-300 rounded-lg">
                <Picker
                  selectedValue={selectedExternalWallet}
                  onValueChange={(itemValue) =>
                    setSelectedExternalWallet(itemValue)
                  }
                  style={{ height: 50, width: "100%" }}
                >
                  <Picker.Item label="Select external wallets" value="" />
                  {savedExternalWalletsArray.map((item, index) => (
                    <Picker.Item
                      key={index}
                      label={`${item.wallet_address.slice(
                        0,
                        8
                      )}...${item.wallet_address.slice(-8)}`}
                      value={item.wallet_address}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            {/* Amount */}
            <View className="mb-4">
              <TextInput
                className="border border-gray-300 rounded-lg px-3 py-2 h-12"
                placeholder="Amount"
                keyboardType="numeric"
                value={amount}
                onChangeText={setAmount}
              />
            </View>

            {/* 2FA Code */}
            <View className="mb-4">
              <TextInput
                className="border border-gray-300 rounded-lg px-3 py-2 h-12"
                placeholder="Enter 2FA verification code"
                keyboardType="numeric"
                value={verificationCode}
                onChangeText={setVerificationCode}
              />
              <TouchableOpacity
                onPress={() => {
                  router.push("/profile");
                  onClose();
                }}
              >
                <Text className="text-blue-500 text-sm mt-2 text-center cursor-pointer">
                  No 2FA? Setup now
                </Text>
              </TouchableOpacity>
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              className="bg-blue-500 py-3 rounded-lg"
              onPress={handleCreateWithdrawlReq}
            >
              <Text className="text-white text-center font-bold">
                Request Withdraw
              </Text>
            </TouchableOpacity>

            {/* Close Button */}
            <TouchableOpacity
              className="mt-4 py-3 rounded-lg border border-gray-300"
              onPress={onClose}
            >
              <Text className="text-gray-700 text-center font-bold">
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default RequestWithdrawModal;
