// // "use client";
// // import React, { useState, useEffect, useCallback } from "react";
// // import { View, Text, StyleSheet } from "react-native";
// // import * as SecureStore from "expo-secure-store";
// // import {
// //   Chat,
// //   Channel,
// //   MessageList,
// //   MessageInput,
// //   useCreateChatClient,
// //   OverlayProvider,
// // } from "stream-chat-expo";
// // import axios from "axios";
// // import { GestureHandlerRootView } from "react-native-gesture-handler";

// // const chatApiKey = "yj2u7nnanuvb";

// // export default function ChatScreen({ orderNumber }) {
// //   const [userData, setUserData] = useState(null);
// //   const [channel, setChannel] = useState(null);
// //   const [isLoading, setIsLoading] = useState(true);

// //   // Initialize chat client with just the API key
// //   const chatClient = useCreateChatClient({
// //     apiKey: chatApiKey,
// //   });

// //   const getValueFor = async (key) => {
// //     try {
// //       const value = await SecureStore.getItemAsync(key);
// //       console.log(`Retrieved ${key}:`, value);
// //       return value;
// //     } catch (error) {
// //       console.error(`Error retrieving ${key}:`, error);
// //       return null;
// //     }
// //   };

// //   const registerUser = useCallback(async (email, orderNumber) => {
// //     if (!email || !orderNumber) {
// //       console.log("Missing email or orderNumber", { email, orderNumber });
// //       return;
// //     }

// //     try {
// //       const BaseURL = process.env.EXPO_PUBLIC_Base_URL;
// //       console.log("Registering user:", { email, orderNumber, BaseURL });

// //       const response = await axios.post(`${BaseURL}/chat-info/`, {
// //         order_number: orderNumber,
// //         email: email,
// //       });

// //       if (response.status === 200 && response.data.data) {
// //         const { user_id, channel_id, chat_token, peername } =
// //           response.data.data;
// //         console.log("Registration successful:", {
// //           user_id,
// //           channel_id,
// //           peername,
// //         });

// //         setUserData({
// //           chatUserId: user_id,
// //           chatUserName: peername,
// //           chatToken: chat_token,
// //           channel_id: channel_id,
// //           email: email,
// //         });
// //       }
// //     } catch (error) {
// //       console.error("Registration error:", error.response || error);
// //       setIsLoading(false);
// //     }
// //   }, []);

// //   // Initialize user data
// //   useEffect(() => {
// //     const initializeUser = async () => {
// //       try {
// //         const userEmail = await getValueFor("userEmail");
// //         if (userEmail) {
// //           await registerUser(userEmail, orderNumber);
// //         } else {
// //           console.log("No email found in SecureStore");
// //           setIsLoading(false);
// //         }
// //       } catch (error) {
// //         console.error("Error initializing user:", error);
// //         setIsLoading(false);
// //       }
// //     };

// //     initializeUser();
// //   }, [orderNumber, registerUser]);

// //   // Connect user to chat
// //   useEffect(() => {
// //     const connectUser = async () => {
// //       if (!chatClient || !userData?.chatUserId || !userData?.chatToken) {
// //         return;
// //       }

// //       try {
// //         console.log("Connecting user:", userData.chatUserId);
// //         await chatClient.disconnectUser();
// //         await chatClient.connectUser(
// //           {
// //             id: userData.chatUserId,
// //             name: userData.chatUserName || "",
// //           },
// //           userData.chatToken
// //         );
// //         console.log("User connected successfully");

// //         // Create and watch channel
// //         if (userData.channel_id) {
// //           const newChannel = chatClient.channel(
// //             "messaging",
// //             userData.channel_id,
// //             {
// //               name: `Chat with ${userData.chatUserName}`,
// //             }
// //           );
// //           await newChannel.watch();
// //           setChannel(newChannel);
// //         }

// //         setIsLoading(false);
// //       } catch (error) {
// //         console.error("Error in chat setup:", error);
// //         setIsLoading(false);
// //       }
// //     };

// //     connectUser();
// //   }, [chatClient, userData]);

// //   if (isLoading) {
// //     return (
// //       <GestureHandlerRootView style={{ flex: 1 }}>
// //         <View style={styles.loadingContainer}>
// //           <Text>Loading Chat...</Text>
// //         </View>
// //       </GestureHandlerRootView>
// //     );
// //   }

// //   if (!userData || !channel) {
// //     return (
// //       <GestureHandlerRootView style={{ flex: 1 }}>
// //         <View style={styles.loadingContainer}>
// //           <Text>Unable to initialize chat. Please try again later.</Text>
// //         </View>
// //       </GestureHandlerRootView>
// //     );
// //   }

// //   return (
// //     <GestureHandlerRootView style={{ flex: 1 }}>
// //       <OverlayProvider>
// //         <Chat client={chatClient}>
// //           <Channel channel={channel}>
// //             <View style={styles.container}>
// //               <View style={styles.header}>
// //                 <Text style={styles.headerTitle}>Peer to Peer Chat</Text>
// //                 <Text style={styles.headerSubTitle}>
// //                   Welcome, {userData.chatUserName}
// //                 </Text>
// //               </View>
// //               <MessageList />
// //               <MessageInput />
// //             </View>
// //           </Channel>
// //         </Chat>
// //       </OverlayProvider>
// //     </GestureHandlerRootView>
// //   );
// // }

// // const styles = StyleSheet.create({
// //   container: {
// //     flex: 1,
// //     width: "100%",
// //     height: "100%",
// //   },
// //   loadingContainer: {
// //     flex: 1,
// //     justifyContent: "center",
// //     alignItems: "center",
// //   },
// //   header: {
// //     backgroundColor: "#f4f4f4",
// //     padding: 15,
// //     borderBottomWidth: 1,
// //     borderBottomColor: "#ddd",
// //   },
// //   headerTitle: {
// //     fontSize: 18,
// //     fontWeight: "bold",
// //   },
// //   headerSubTitle: {
// //     fontSize: 14,
// //     color: "#777",
// //   },
// // });

// // HHHHHEEEEEEEEEEEEERRRRRRRRRRRRRRRRRRRREEEEEEEEEEEEEEEEEE*****************************----------------------------*(************************************)
// "use client";
// import React, { useState, useEffect } from "react";
// import { View, Text, StyleSheet } from "react-native";
// import * as SecureStore from "expo-secure-store";
// import {
//   Chat,
//   Channel,
//   MessageList,
//   MessageInput,
//   useCreateChatClient,
//   OverlayProvider,
// } from "stream-chat-expo";
// import axios from "axios";
// import { GestureHandlerRootView } from "react-native-gesture-handler";
// const chatApiKey = "yj2u7nnanuvb";

// export default function ChatScreen({ orderNumber }) {
//   const [userData, setUserData] = useState(null);
//   const [channel, setChannel] = useState(null);

//   // ✅ Call useCreateChatClient unconditionally
//   const chatClient = useCreateChatClient({
//     apiKey: "yj2u7nnanuvb",
//     userData: {
//       id: userData?.chatUserId,
//       name: userData?.chatUserName,
//     },
//     tokenOrProvider: userData?.chatToken,
//   });

//   // SecureStore function to retrieve stored values
//   const getValueFor = async (key) => {
//     try {
//       return await SecureStore.getItemAsync(key);
//     } catch (error) {
//       console.error(`Error retrieving ${key}:`, error);
//     }
//   };

//   // Fetch chat-related user data
//   const registerUser = async (email, orderNumber) => {
//     try {
//       const BaseURL = process.env.EXPO_PUBLIC_Base_URL;

//       const response = await axios.post(`${BaseURL}/chat-info/`, {
//         order_number: orderNumber,
//         email: email,
//       });

//       if (response.status === 200) {
//         const { user_id, channel_id, chat_token, peername } =
//           response.data.data;
//         setUserData({
//           chatUserId: user_id,
//           chatUserName: peername,
//           chatToken: chat_token,
//           channel_id: channel_id,
//           email: email,
//         });
//       } else {
//         console.error("Login unsuccessful: ", response.data.message);
//       }
//     } catch (error) {
//       console.error("Error during registration: ", error);
//     }
//   };

//   // Fetch user email from SecureStore
//   useEffect(() => {
//     const fetchUserData = async () => {
//       const userEmail = await getValueFor("userEmail");
//       if (userEmail) {
//         registerUser(userEmail, orderNumber);
//       }
//     };

//     fetchUserData();
//   }, [orderNumber]);

//   // ✅ Ensure channel is created only when chatClient is ready
//   useEffect(() => {
//     if (chatClient && userData?.channel_id) {
//       const newChannel = chatClient.channel("messaging", userData.channel_id, {
//         name: `Chat with ${userData.chatUserName}`,
//       });
//       newChannel.watch().then(() => setChannel(newChannel));
//     }
//   }, [chatClient, userData?.channel_id]);

//   if (!channel) {
//     return (
//       <View style={styles.loadingContainer}>
//         <Text>Loading Chat...</Text>
//       </View>
//     );
//   }

//   return (
//     <GestureHandlerRootView style={{ flex: 1 }}>
//       <OverlayProvider>
//         <Chat client={chatClient}>
//           <Channel channel={channel}>
//             <View style={styles.container}>
//               <View style={styles.header}>
//                 <Text style={styles.headerTitle}>Peer to Peer Chat</Text>
//                 <Text style={styles.headerSubTitle}>
//                   Welcome, {userData?.chatUserName}
//                 </Text>
//               </View>
//               <MessageList />
//               <MessageInput />
//             </View>
//           </Channel>
//         </Chat>
//       </OverlayProvider>
//     </GestureHandlerRootView>
//   );
// }

// const styles = StyleSheet.create({
//   container: { width: "100%" },
//   loadingContainer: { flex: 1, justifyContent: "center", alignItems: "center" },
//   header: {
//     backgroundColor: "#f4f4f4",
//     padding: 15,
//     borderBottomWidth: 1,
//     borderBottomColor: "#ddd",
//   },
//   headerTitle: { fontSize: 18, fontWeight: "bold" },
//   headerSubTitle: { fontSize: 14, color: "#777" },
// });
