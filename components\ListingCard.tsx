import React from "react";
import { View, Text, Switch, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

interface Listing {
  available_liquidity: number;
  country_from_id: number | null;
  country_to_id: number | null;
  created_date: string;
  currency_accepted: {
    currency_code: string;
    currency_name: string;
    id: number;
  };
  currency_payout: {
    currency_code: string;
    currency_name: string;
    id: number;
  };
  details: string | null;
  final_trade_fee: number;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  max_liquidity: number;
  min_liquidity: number;
  payin_option: {
    id: number;
    payment_method: string;
  };
  payout_option: {
    id: number;
    payment_method: string;
  };
  terms_and_conditions: string;
  time_limit: number;
  trade_fee: number;
  user_payment_option_id: number | null;
}

interface toggleListingProp {
  (id: number): void;
}

const ListingCardComponent = React.memo(
  ({
    listing,
    toggleListing,
  }: {
    listing: Listing;
    toggleListing: toggleListingProp;
  }) => {
    const router = useRouter();

    const handleEditListing = () => {
      // console.log("dudu", `/editlisting/${listing.id}`);
      const queryParams = new URLSearchParams({
        currencyAccepted: listing.currency_accepted.currency_code,
        currencyPayout: listing.currency_payout.currency_code,
        availableLiquidity: listing.available_liquidity.toString(),
        minLiquidity: listing.min_liquidity.toString(),
        maxLiquidity: listing.max_liquidity.toString(),
        fxRate: listing.indicative_fx_rate.toString(),
        finalTradeFee: listing.final_trade_fee.toString(),
        trade_fee_percent: listing.trade_fee.toString(),
        payIn: listing.payin_option.payment_method,
        payOut: listing.payout_option.payment_method,
        time_limit: listing.time_limit.toString(),
        user_payment_option_id: (
          listing.user_payment_option_id || ""
        ).toString(),
        terms: listing.terms_and_conditions,
      }).toString();

      router.push(`/editlisting/${listing.id}` as any);
    };

    return (
      <View key={listing.id} className="bg-white p-4 rounded-lg mb-4">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-orange-500 text-md">
            Listing Id: {listing.id}
          </Text>
          <View className="flex-row items-center">
            <Switch
              value={listing.is_deleted}
              onValueChange={() => toggleListing(listing.id)}
              trackColor={{ false: "#4153ed", true: "#db2012" }}
              thumbColor={listing.is_deleted ? "#f4f3f4" : "#f4f3f4"}
            />
            <TouchableOpacity className="ml-2" onPress={handleEditListing}>
              <Ionicons name="pencil" size={24} color="#4B5563" />
            </TouchableOpacity>
          </View>
        </View>
        <View className="space-y-2">
          <View className="flex-row justify-between">
            <Text className="text-md">Currency accepted</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.currency_accepted.currency_code}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Currency of payout</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.currency_payout.currency_code}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Available Liquidity</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.available_liquidity}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Minimum Liquidity</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.min_liquidity}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Maximum Liquidity</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.max_liquidity}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Indicative FX Rate</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.indicative_fx_rate.toFixed(2)}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Final Trade fee</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.final_trade_fee}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">PayIn Method</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.payin_option?.payment_method}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">PayOut Method</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing.payout_option?.payment_method}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-md">Time Limit</Text>
            <Text className="text-red-500 text-md font-semibold">
              {listing?.time_limit} mins
            </Text>
          </View>
        </View>
      </View>
    );
  }
);

export default ListingCardComponent;
