import { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Pressable,
} from "react-native";

import { Picker } from "@react-native-picker/picker";
import { router } from "expo-router";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";

interface externalWallet {
  wallet_address: string;
  network: string;
}

interface RequestWithdrawModalProps {
  setEditSuccess: (value: boolean) => void;
}

const RequestWithdrawModal = ({
  setEditSuccess,
}: RequestWithdrawModalProps) => {
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [walletAddress, setWalletAddress] = useState("");
  const [amount, setAmount] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [savedExternalWalletsArray, setSavedExternalWalletsArray] = useState<
    externalWallet[]
  >([]);
  const [selectedExternalWallet, setSelectedExternalWallet] = useState("");

  const getExternalWallets = async () => {
    try {
      const res = await customFetchWithToken.get("/get-external-wallet/");
      console.log("externalWallets", res.data.data);
      setSavedExternalWalletsArray(res.data.data);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCreateWithdrawlReq = async () => {
    try {
      const res = await customFetchWithToken.post("/create-withdraw-request/", {
        amount: Number(amount),
        address: selectedExternalWallet,
      });
      console.log("resC", res);
      showToastSuccess(res.data.message);
      setEditSuccess(true);
    } catch (error: any) {
      console.log(error);
      showToastError(error.response.data.message);
    }
  };

  useEffect(() => {
    getExternalWallets();
  }, []);
  return (
    <View className="overflow-y-scroll">
      {/* <View className="bg-white rounded-lg p-6 w-11/12"> */}
      <Text className="text-lg font-bold text-center mb-4">
        Request Withdraw
      </Text>

      {/* Select Network */}
      <View className="mb-4">
        <Text className="text-gray-700 mb-2">Select Network</Text>
        <Picker
          selectedValue={selectedNetwork}
          onValueChange={(itemValue) => setSelectedNetwork(itemValue)}
          style={{ height: 50, width: "100%" }}
        >
          <Picker.Item label="Select Network" value="" />
          <Picker.Item label="ERC/MATIC/BNB" value="ERC" />
          <Picker.Item label="TRC" value="TRC" />
        </Picker>
      </View>

      {/* Wallet Address */}
      <View className="mb-4">
        <Text className="text-gray-700 mb-2">Select External Wallet</Text>
        <Picker
          selectedValue={selectedExternalWallet}
          onValueChange={(itemValue) => setSelectedExternalWallet(itemValue)}
          style={{ height: 50, width: "100%" }}
        >
          <Picker.Item label="Select external wallets" value="" />
          {savedExternalWalletsArray.map((item, index) => (
            <Picker.Item
              key={index}
              label={`${item.wallet_address.slice(
                0,
                8
              )}...${item.wallet_address.slice(-8)}`}
              value={item.wallet_address}
            />
          ))}
        </Picker>
      </View>

      {/* Amount */}
      <View className="mb-4">
        <TextInput
          className="border border-gray-300 rounded-lg px-3 py-2"
          placeholder="Amount"
          maxLength={10}
          keyboardType="numeric"
          value={amount}
          onChangeText={setAmount}
        />
      </View>

      {/* 2FA Code */}
      <View className="mb-4">
        <TextInput
          className="border border-gray-300 rounded-lg px-3 py-2"
          placeholder="Enter 2FA verification code"
          maxLength={10}
          keyboardType="numeric"
          value={verificationCode}
          onChangeText={setVerificationCode}
        />
        <TouchableOpacity
          onPress={() => {
            router.push("/profile");
            // onClose();
          }}
        >
          <Text className="text-blue-500 text-sm mt-2 text-center cursor-pointer">
            No 2FA? Setup now
          </Text>
        </TouchableOpacity>
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        className="bg-blue-500 py-3 rounded-lg"
        onPress={handleCreateWithdrawlReq}
      >
        <Text className="text-white text-center font-bold">
          Request Withdraw
        </Text>
      </TouchableOpacity>
      {/* <Toast config={toastConfig} /> */}
    </View>
  );
};

export default RequestWithdrawModal;
